<template>
  <template v-if="isConversationMarkSync" />
  <view
    v-else
    :class="shouldShowButtons ? 'gap-52rpx' : 'gap-26rpx'"
    class="flex flex-col items-center py-26rpx mt-42rpx"
  >
    <view class="w-482rpx box-unset relative center">
      <view
        class="bg-#ffffff min-h-158rpx flex flex-col items-center gap-18rpx w-full border-rd-16rpx shadow-[0rpx_10rpx_40rpx_0rpx_rgba(0,0,0,0.15)]"
      >
        <view class="mt--42rpx">
          <view class="center size-96rpx bg-#ffffff rounded-50%">
            <view
              :style="{ backgroundColor: iconInfo?.bgColor }"
              class="center size-76rpx rounded-50%"
            >
              <wd-icon :name="iconInfo?.icon" size="44rpx" />
            </view>
          </view>
        </view>
        <view v-if="showCustomTips" class="flex flex-col items-center gap-8rpx">
          <text class="c-#000000 text-28rpx">{{ getCustomTips }}</text>
          <text
            v-if="isInterviewAppointment"
            :class="{ ' mb-20rpx': !isExchangeAccepted }"
            class="c-#888888 text-28rpx"
          >
            {{ valueOfToDate(customExts?.interviewTime, 'YYYY年MM月DD日 HH:mm') }}
          </text>
        </view>
        <!-- 电话号码显示 -->
        <view
          v-if="isExchangeAccepted && isPhoneExchange"
          class="flex flex-col items-center gap-8rpx"
        >
          <text class="c-#666666 text-24rpx">对方手机号：{{ contactPhone }}</text>
        </view>
        <!-- 微信号显示 -->
        <view
          v-if="isExchangeAccepted && isWechatExchange"
          class="flex flex-col items-center gap-8rpx"
        >
          <text class="c-#666666 text-24rpx">对方微信号：{{ contactWechat }}</text>
        </view>
        <view v-if="shouldShowButtons && validButtonListBool" class="center gap-66rpx mb--32rpx">
          <view
            v-for="button in validButtonList"
            :key="button.action"
            :class="{
              [button.bgClass]: true,
              'w-120rpx': validButtonList.length > 1,
              'w-136rpx': validButtonList.length === 1,
            }"
            class="rounded-20rpx center w-120rpx h-62rpx"
            @click="handleButtonClick(button.type, button.action)"
          >
            <text :class="button.textClass" class="font500 text-24rpx">{{ button.text }}</text>
          </view>
        </view>
      </view>
    </view>
    <view class="flex items-center gap-12rpx">
      <wd-img :src="warningImg" height="30rpx" width="26rpx" />
      <text class="c-#888888 text-22rpx">脱离平台沟通，风险损失需自行承担</text>
    </view>
  </view>
</template>

<script lang="ts" setup>
import { CommonUtil } from 'wot-design-uni'
import { EMIT_EVENT } from '@/enum'
import { valueOfToDate } from '@/utils'
import type { MixedMessageBody } from '@/ChatUIKit/types'
import { resumeQueryMyFileResumeList } from '@/service/resume'
import { imModifyCustomMessage } from '@/service/im'
import { hrExchangeResumeRecordDoResume } from '@/service/hrExchangeResumeRecord'
import { hrExchangeNumberDoExchange } from '@/service/hrExchangeNumber'
import { hrExchangeCodeDoExchange } from '@/service/hrExchangeCode'
import { exchangeNumberDoExchange } from '@/service/exchangeNumber'
import { exchangeResumeRecordDoResume } from '@/service/exchangeResumeRecord'
import { exchangeCodeDoExchange } from '@/service/exchangeCode'
import { userInterviewRecordDeal } from '@/service/userInterviewRecord'
import { userGetWxCode } from '@/service/user'
import sendPhoneImg from '@/ChatUIKit/static/message-custom/send-phone.png'
import sendResumeImg from '@/ChatUIKit/static/message-custom/send-resume.png'
import sendWXImg from '@/ChatUIKit/static/message-custom/wx-white.png'
import infoInterviewImg from '@/ChatUIKit/static/message-custom/info-interview.png'
import warningImg from '@/ChatUIKit/static/message-custom/warning.png'

type ExtType = Exclude<Api.IM.CustomMessage.ExtType, 'uninterested' | 'conversation_mark_sync'>

interface Props {
  msg: MixedMessageBody
}

interface IconListValInt {
  icon: string
  bgColor: string
}

interface ButtonItem {
  text: string
  action: string
  type: 'accept' | 'reject' | 'view' | 'copy'
  bgClass: string
  textClass: string
}

const props = defineProps<Props>()

const { getIMLoginId, autoUpdateConversationMark, getConversationInfo } = useIMConversation()
const { userRoleIsBusiness, userIntel } = useUserInfo()
const { sm4DecryptToString, sm4Encrypt } = useSmCrypto({
  type: 'password',
})
const {
  bool: validButtonListBool,
  setFalse: validButtonListBoolFalse,
  setTrue: validButtonListBoolTrue,
} = useBoolean()

const iconList: Record<ExtType, IconListValInt> = {
  resume: {
    icon: sendResumeImg,
    bgColor: '#FF6021',
  },
  exchange_phone: {
    icon: sendPhoneImg,
    bgColor: '#4075FF',
  },
  exchange_wechat: {
    icon: sendWXImg,
    bgColor: '#4EC200',
  },
  interview_appointment: {
    icon: infoInterviewImg,
    bgColor: '#4075FF',
  },
}

const msgInfo = computed(() => props.msg)
const customExts = computed(
  () =>
    ((msgInfo.value.type === 'custom' && msgInfo.value.customExts) ||
      {}) as Api.IM.CustomMessage.ExtInfo,
)
const iconInfo = computed(() => iconList[customExts.value.type] as IconListValInt)
const formIsMine = computed(() => msgInfo.value.from === getIMLoginId.value)
const isExchangeAccepted = computed(() => customExts.value.status === 1)
const isPhoneExchange = computed(() => customExts.value.type === 'exchange_phone')
const isWechatExchange = computed(() => customExts.value.type === 'exchange_wechat')
const isInterviewAppointment = computed(() => customExts.value.type === 'interview_appointment')
const isConversationMarkSync = computed(() => customExts.value.type === 'conversation_mark_sync')
const showCustomTips = computed(
  () => !(isExchangeAccepted.value && (isPhoneExchange.value || isWechatExchange.value)),
)
const contactPhone = computed(() => {
  if (!isExchangeAccepted.value || !isPhoneExchange.value) return ''
  return sm4DecryptToString(
    userRoleIsBusiness.value ? customExts.value.cUserPhone : customExts.value.bUserPhone,
  )
})
const contactWechat = computed(() => {
  if (!isExchangeAccepted.value || !isWechatExchange.value) return ''
  return sm4DecryptToString(
    userRoleIsBusiness.value ? customExts.value.cUserWechat : customExts.value.bUserWechat,
  )
})
const getCustomTips = computed(() => {
  const type = customExts.value.type
  const status = customExts.value.status
  const isSender = formIsMine.value
  const messageTypeHandlers: Record<ExtType, () => void> = {
    resume: () => {
      const statusHandlers = {
        /** 发送者 */
        sender: {
          0: userRoleIsBusiness.value ? '等待对方发送简历' : '已发送简历',
          1: userRoleIsBusiness.value ? '对方已同意发送简历' : '对方已同意您的简历请求',
          // 2: userRoleIsBusiness.value ? '对方拒绝发送简历' : '对方已拒绝您的简历请求',
          2: userRoleIsBusiness.value ? '等待对方发送简历' : '已发送简历',
        },
        /** 接收者 */
        receiver: {
          0: userRoleIsBusiness.value ? '对方发送了简历' : '对方请求您的简历',
          1: userRoleIsBusiness.value ? '对方简历' : '我的简历',
          2: userRoleIsBusiness.value ? '您已拒绝对方的简历' : '您拒绝了对方的简历请求',
        },
      }
      const roleHandler = isSender ? statusHandlers.sender : statusHandlers.receiver
      return roleHandler[status] ?? ''
    },
    exchange_phone: () => {
      const statusHandlers = {
        /** 发送者 */
        sender: {
          0: '已发送交换电话请求',
          1: '对方同意您的交换电话请求',
          // 2: '对方拒绝您的交换电话请求',
          2: '已发送交换电话请求',
        },
        /** 接收者 */
        receiver: {
          0: '对方请求交换电话',
          1: '您同意了对方的交换电话请求',
          2: '您拒绝了对方的交换电话请求',
        },
      }
      const roleHandler = isSender ? statusHandlers.sender : statusHandlers.receiver
      return roleHandler[status] ?? ''
    },
    exchange_wechat: () => {
      const statusHandlers = {
        /** 发送者 */
        sender: {
          0: '已发送交换微信请求',
          1: '对方同意您的交换微信请求',
          // 2: '对方拒绝您的交换微信请求',
          2: '已发送交换微信请求',
        },
        /** 接收者 */
        receiver: {
          0: '对方请求交换微信',
          1: '您同意了对方的交换微信请求',
          2: '您拒绝了对方的交换微信请求',
        },
      }
      const roleHandler = isSender ? statusHandlers.sender : statusHandlers.receiver
      return roleHandler[status] ?? ''
    },
    interview_appointment: () => {
      const statusHandlers = {
        /** 发送者 */
        sender: {
          0: '发起面试',
          1: '邀约面试',
          // 2: '对方拒绝了您的面试邀约',
          2: '发起面试',
        },
        /** 接收者 */
        receiver: {
          0: '对方发起面试',
          1: '邀约面试',
          2: '您拒绝了对方的面试邀约',
        },
      }
      const roleHandler = isSender ? statusHandlers.sender : statusHandlers.receiver
      return roleHandler[status] ?? ''
    },
  }
  const handler = messageTypeHandlers[type] || (() => '自定义消息')
  return handler()
})

const BUTTON_CONFIGS = {
  pending: [
    { text: '拒绝', type: 'reject', bgClass: 'bg-#FDC6C6', textClass: 'c-#FF0C0C' },
    { text: '同意', type: 'accept', bgClass: 'bg-#BFFFC7', textClass: 'c-#2DB01C' },
  ],
  accepted: {
    resume: { text: '立即查看', type: 'view', bgClass: 'bg-#FF6021', textClass: 'c-#FFFFFF' },
    exchange_phone: {
      text: '点击复制',
      type: 'copy',
      bgClass: 'bg-#4075FF',
      textClass: 'c-#FFFFFF',
    },
    exchange_wechat: {
      text: '点击复制',
      type: 'copy',
      bgClass: 'bg-#4EC200',
      textClass: 'c-#FFFFFF',
    },
    interview_appointment: {
      text: '立即查看',
      type: 'view',
      bgClass: 'bg-#4075FF',
      textClass: 'c-#FFFFFF',
    },
  },
} as const
const validButtonList = computed((): ButtonItem[] => {
  const { type, status } = customExts.value
  if (!type || !iconList[type] || status === undefined || status === null) {
    return []
  }
  if (!Object.keys(iconList).includes(type)) {
    return []
  }
  // 状态0且不是发送者，显示操作按钮
  if (status === 0 && !formIsMine.value) {
    return BUTTON_CONFIGS.pending.map((btn) => ({
      ...btn,
      action: `${btn.type}_${type}`,
    }))
  }

  // 状态1且有对应的操作配置，显示单个操作按钮
  if (status === 1 && BUTTON_CONFIGS.accepted[type]) {
    validButtonListBoolTrue()
    const config = BUTTON_CONFIGS.accepted[type]
    return [
      {
        ...config,
        action: `${config.type}_${type}`,
      },
    ]
  }

  return []
})

const shouldShowButtons = computed(() => validButtonList.value.length > 0)

const updateMessageStatus = async (
  status: Exclude<Api.IM.CustomMessage.StatusType, 0>,
  exts: Partial<Api.IM.CustomMessage.ExtInfo> = {},
) => {
  await imModifyCustomMessage({
    customExts: {
      ...customExts.value,
      ...exts,
      status,
    },
    msgId: msgInfo.value.id,
  })
}

const handleButtonClick = (type: ButtonItem['type'], action: string) => {
  const messageType = customExts.value.type
  const isBusiness = userRoleIsBusiness.value

  const actionMap = {
    accept: async () => {
      let additionalExts: Partial<Api.IM.CustomMessage.ExtInfo> = {}
      const acceptHandlers = {
        resume: async () => {
          if (isBusiness) {
            await hrExchangeResumeRecordDoResume({
              id: customExts.value.exchange_resume_record_id,
              status: 1,
            })
          } else {
            const { data } = await resumeQueryMyFileResumeList()
            if (!data || !data.length) {
              uni.$emit(EMIT_EVENT.PROMPT_COMPLETE_RESUME)
              return Promise.reject(new Error('请先完善简历信息'))
            }
            const [{ fileUrl: attachmentUrl }] = data
            additionalExts.cUserResumeLink = sm4Encrypt(attachmentUrl)
          }
        },
        exchange_phone: async () => {
          const sm4PhoneCode = sm4Encrypt(userIntel.value.phone)
          if (isBusiness) {
            await hrExchangeNumberDoExchange({
              id: customExts.value.exchange_phone_record_id,
              status: 1,
            })
            additionalExts.bUserPhone = sm4PhoneCode
          } else {
            await exchangeNumberDoExchange({
              id: customExts.value.exchange_phone_record_id,
              status: 1,
            })
            additionalExts.cUserPhone = sm4PhoneCode
          }
        },
        exchange_wechat: async () => {
          const { data: wxCode } = await userGetWxCode()
          if (!wxCode) {
            uni.$emit(EMIT_EVENT.PROMPT_ADD_WECHAT)
            return Promise.reject(new Error('微信号未设置'))
          }
          const sm4WeChatCode = sm4Encrypt(wxCode)
          if (isBusiness) {
            await hrExchangeCodeDoExchange({
              id: customExts.value.exchange_wechat_record_id,
              status: 1,
            })
            additionalExts.bUserWechat = sm4WeChatCode
          } else {
            await exchangeCodeDoExchange({
              id: customExts.value.exchange_wechat_record_id,
              status: 1,
            })
            additionalExts.cUserWechat = sm4WeChatCode
          }
        },
        interview_appointment: async () => {
          if (!isBusiness) {
            await userInterviewRecordDeal({
              id: customExts.value.interview_appointment_record_id,
              status: 1,
              msgJson: JSON.stringify({
                ...customExts.value,
                status: 1,
              }),
            })
          }
        },
      }
      console.log(
        `执行操作: ${action}, 消息类型: ${messageType}, 用户角色: ${isBusiness ? 'B端' : 'C端'}`,
      )
      await acceptHandlers[messageType]?.()
      const conversationId = getConversationInfo.value.conversationId || msgInfo.value.from
      await updateMessageStatus(1, additionalExts)
      autoUpdateConversationMark(conversationId, messageType, 1)
    },
    reject: async () => {
      const rejectHandlers = {
        resume: async () => {
          if (isBusiness) {
            await hrExchangeResumeRecordDoResume({
              id: customExts.value.exchange_resume_record_id,
              status: 2,
            })
          } else {
            await exchangeResumeRecordDoResume({
              id: customExts.value.exchange_resume_record_id,
              status: 2,
            })
          }
        },
        exchange_phone: async () => {
          if (isBusiness) {
            await hrExchangeNumberDoExchange({
              id: customExts.value.exchange_phone_record_id,
              status: 2,
            })
          } else {
            await exchangeNumberDoExchange({
              id: customExts.value.exchange_phone_record_id,
              status: 2,
            })
          }
        },
        exchange_wechat: async () => {
          if (isBusiness) {
            await hrExchangeCodeDoExchange({
              id: customExts.value.exchange_wechat_record_id,
              status: 2,
            })
          } else {
            await exchangeCodeDoExchange({
              id: customExts.value.exchange_wechat_record_id,
              status: 2,
            })
          }
        },
        interview_appointment: async () => {
          if (!isBusiness) {
            await userInterviewRecordDeal({
              id: customExts.value.interview_appointment_record_id,
              status: 2,
              msgJson: JSON.stringify({
                ...customExts.value,
                status: 2,
              }),
            })
          }
        },
      }
      await rejectHandlers[messageType]?.()
      await updateMessageStatus(2)
    },
    view: () => {
      const viewHandlers = {
        resume: () => {
          const fileUrl = sm4DecryptToString(customExts.value.cUserResumeLink)
          uni.navigateTo({
            url: `/resumeRelated/AttachmentResume/WebViewpdf?fileUrl=${fileUrl}`,
          })
        },
        interview_appointment: () => {
          uni.navigateTo({
            url: CommonUtil.buildUrlWithParams('/sub_business/pages/interview/detail', {
              id: `${customExts.value.interview_appointment_record_id}`,
              toType: isBusiness ? 'toBusiness' : 'toPerson',
            }),
          })
        },
      }
      viewHandlers[messageType]?.()
    },
    copy: () => {
      const copyHandlers = {
        exchange_phone: () => {
          if (!contactPhone.value) return
          uni.setClipboardData({
            data: contactPhone.value,
            success: () => {
              uni.showToast({
                title: '电话号码已复制',
                icon: 'none',
              })
            },
          })
        },
        exchange_wechat: () => {
          if (!contactWechat.value) return
          uni.setClipboardData({
            data: contactWechat.value,
            success: () => {
              uni.showToast({
                title: '微信号已复制',
                icon: 'none',
              })
            },
          })
        },
      }
      copyHandlers[messageType]?.()
    },
  }
  actionMap[type]?.()
  console.log(
    `执行操作: ${action}, 消息类型: ${messageType}, 用户角色: ${isBusiness ? 'B端' : 'C端'}`,
  )
}
watchEffect(async () => {
  await nextTick()
  await CommonUtil.pause(200)
  if (shouldShowButtons.value) {
    validButtonListBoolTrue()
  } else {
    validButtonListBoolFalse()
  }
})
</script>

<style lang="scss" scoped>
//
</style>
