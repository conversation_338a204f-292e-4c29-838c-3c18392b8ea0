import { PropCardType, PayPropId } from '@/enum'

export enum DurationType {
  /** 小时 */
  HOUR = 0,
  /** 日 */
  DAY = 1,
  /** 月 */
  MONTH = 2,
  /** 年 */
  YEAR = 3,
  /** 人 */
  PERSON = 4,
}

export interface payPropQueryUsingCountListInt {
  [key: string]: string | number
}

export interface payPropQueryUsingCountListDataInt {}

export interface payPropQueryUsingPositionListDataInt {
  /** 道具ID */
  propId: PropCardType
}

export interface payPropQueryUsingPositionListInt {
  /** 到期时间 */
  endTime?: string
  /** 岗位主键id */
  id?: number
  /** 岗位名称 */
  positionName?: string
  /** 例如12薪资 */
  salaryMonths?: number
  /** 薪资范围-最低0为不限 */
  workSalaryBegin?: number
  /** 薪资范围-最高0为不限 */
  workSalaryEnd?: number
}

export interface payPropQueryPropCategoryListByPositionDataInt {
  /** 岗位id */
  positionId: number
  /** 道具id */
  propId: PropCardType
}

export interface payPropQueryPropCategoryListByPositionInt {
  /** 类别详情描述 */
  categoryDesc?: string
  /** 类别名 */
  categoryName?: string
  /** 持续时长(对应type的时、日、月、年) */
  durationTime?: number
  /** 类型0:小时1:日2:月3:年 */
  durationType?: DurationType
  /** 主键id */
  id?: PayPropId | number
  /** 原价(单位:分) */
  originalPrice?: number
  /** 实际售价(单位:分) */
  price?: number
  /** 道具主键id */
  propId?: number
}
