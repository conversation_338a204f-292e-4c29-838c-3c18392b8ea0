<route lang="json5">
{
  layout: 'default',
  style: {
    navigationBarTitleText: '优惠卷',
    enablePullDownRefresh: false,
    navigationStyle: 'custom',
  },
}
</route>
<template>
  <z-paging ref="pagingRef" v-model="pageData" :paging-style="pageStyle" @query="queryList">
    <template #top>
      <CustomNavBar title="优惠卷"></CustomNavBar>
    </template>
    <view class="coupons-box">
      <!-- 分类标题 -->
      <view v-if="unusedCoupons && unusedCoupons.length > 0" class="section-title">未使用</view>

      <!-- 未使用优惠券列表 -->
      <view class="coupons-list">
        <view v-for="(coupon, index) in unusedCoupons" :key="index" class="coupon-card">
          <view class="coupon-main">
            <view class="coupon-left">
              <!-- 优惠券金额 -->
              <view class="coupon-amount">
                <text class="currency">{{ coupon.type === 2 ? '折' : '￥' }}</text>
                <text class="amount">{{ handleCouponAmount(coupon) }}</text>
              </view>
            </view>

            <!-- 垂直分割线 -->
            <wd-divider
              class="coupon-divider-vertical"
              color="#ff2b75"
              dashed
              vertical
            ></wd-divider>

            <view class="coupon-right">
              <!-- 优惠券信息 -->
              <view class="coupon-info">
                <view class="coupon-title">{{ coupon.propName }}</view>
                <view class="coupon-desc">{{ handleCouponDesc(coupon) }}</view>
              </view>

              <!-- 使用按钮 -->
              <view class="coupon-action">
                <wd-button custom-class="use-btn" size="small" @click="useCoupon(coupon)">
                  立即使用
                </wd-button>
              </view>
            </view>
          </view>

          <!-- 水平分割线 -->
          <view class="coupon-divider-horizontal">
            <wd-divider color="#ff2b75" dashed></wd-divider>
          </view>

          <!-- 有效期 -->
          <view class="coupon-validity">有效期至{{ coupon.expiredTime }}</view>
        </view>
      </view>

      <!-- 已使用分类 -->
      <view v-if="usedCoupons && usedCoupons.length > 0" class="section-title">已使用</view>
      <view class="coupons-list">
        <view v-for="(coupon, index) in usedCoupons" :key="index" class="coupon-card used-card">
          <view class="coupon-main">
            <view class="coupon-left">
              <view class="coupon-amount used-amount">
                <text class="currency">{{ coupon.type === 2 ? '折' : '￥' }}</text>
                <text class="amount">{{ handleCouponAmount(coupon) }}</text>
              </view>
            </view>

            <!-- 垂直分割线 -->
            <wd-divider
              class="coupon-divider-vertical"
              color="#636363"
              dashed
              vertical
            ></wd-divider>

            <view class="coupon-right">
              <view class="coupon-info used-info">
                <view class="coupon-title">{{ coupon.propName }}</view>
                <view class="coupon-desc">{{ handleCouponDesc(coupon) }}</view>
                <view class="coupon-used-jobs">岗位：{{ coupon.jobs || '电商管理' }}</view>
              </view>
            </view>
          </view>

          <!-- 水平分割线 -->
          <view class="coupon-divider-horizontal">
            <wd-divider color="#636363" dashed></wd-divider>
          </view>

          <view class="coupon-validity coupon-validity-used">
            使用时间{{ coupon.receivedTime }}
          </view>
        </view>
      </view>

      <!-- 已过期分类 -->
      <view v-if="expiredCoupons && expiredCoupons.length > 0" class="section-title">已过期</view>
      <view class="coupons-list">
        <view
          v-for="(coupon, index) in expiredCoupons"
          :key="index"
          class="coupon-card expired-card"
        >
          <view class="coupon-main">
            <view class="coupon-left">
              <view class="coupon-amount expired-amount">
                <text class="currency">{{ coupon.type === 2 ? '折' : '￥' }}</text>
                <text class="amount">{{ handleCouponAmount(coupon) }}</text>
              </view>
            </view>

            <!-- 垂直分割线 -->
            <wd-divider
              color="#636363"
              custom-class="coupon-divider-vertical"
              dashed
              vertical
            ></wd-divider>

            <view class="coupon-right">
              <view class="coupon-info expired-info">
                <view class="coupon-title">{{ coupon.propName }}</view>
                <view class="coupon-desc">{{ handleCouponDesc(coupon) }}</view>
              </view>
            </view>
          </view>

          <!-- 水平分割线 -->
          <view class="coupon-divider-horizontal">
            <wd-divider color="#636363" dashed></wd-divider>
          </view>

          <view class="coupon-validity coupon-validity-expired">
            有效期至{{ coupon.expiredTime }}
          </view>
        </view>
      </view>
    </view>
  </z-paging>
  <wd-toast />
  <wd-message-box />
</template>

<script lang="ts" setup>
import CustomNavBar from '@/components/CustomNavBar/CustomNavBar.vue'
import { queryMyCoupons } from '@/interPost/my'
import WdUpload from 'wot-design-uni/components/wd-upload/wd-upload.vue'

// 页面样式配置
const { pagingRef, pageInfo, pageData, pageStyle, pageSetInfo } = usePaging({
  style: {
    background: 'linear-gradient(155deg, #FFDEDE 0%, #EBEFFA 18%, #FFFFFF 44%, #FFFFFF 100%)',
  },
})

// 模拟优惠券数据
const couponsData = ref([])

const queryList = async (page: number, size: number) => {
  pageSetInfo(page, size)
  const res: any = await queryMyCoupons({
    entity: {
      status: null,
    },
    orderBy: {},
    page: pageInfo.page,
    size: pageInfo.size,
  })
  if (res.code === 0) {
    couponsData.value = res.data.list
  }
  pagingRef.value.complete(res.data.list)
}

// 处理优惠卷金额
const handleCouponAmount = (coupon: any) => {
  if (coupon.type === 2) {
    return coupon.discountValue
  }
  return coupon.minOrderAmount / 100
}

// 处理优惠卷数据
const handleCouponDesc = (coupon: any) => {
  if (coupon.type === 2) {
    return `无门槛折扣卷`
  } else if (coupon.type === 1) {
    return `满500元减${coupon.minOrderAmount / 100}元`
  } else {
    return '无门槛抵扣券'
  }
}

// 页面显示时触发数据加载
onShow(async () => {
  await uni.$onLaunched
  pagingRef.value.reload()
})

// 未使用的优惠券
const unusedCoupons = computed(() => {
  return couponsData.value.filter((coupon) => coupon.status === 0)
})

// 已使用的优惠券
const usedCoupons = computed(() => {
  return couponsData.value.filter((coupon) => coupon.status === 1)
})

// 已过期的优惠券
const expiredCoupons = computed(() => {
  return couponsData.value.filter((coupon) => coupon.status === 2)
})

// 使用优惠券
const useCoupon = (coupon: any) => {
  console.log('使用优惠卷', coupon)
}
</script>

<style lang="scss" scoped>
.coupons-box {
  padding: 0rpx 40rpx;
}

// 分类标题样式
.section-title {
  margin: 40rpx 0 30rpx 0;
  font-size: 32rpx;
  font-weight: normal;
  color: #333333;

  &:first-child {
    margin-top: 20rpx;
  }
}

.used-title,
.expired-title {
  color: #555555;
}

// 优惠券列表样式
.coupons-list {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
  margin-bottom: 40rpx;
}

// 优惠券卡片样式
.coupon-card {
  position: relative;
  margin-bottom: 30rpx;
  overflow: visible; /* 改为 visible 以显示阴影 */
  background: url('@/static/my/business/coupons_normal.png') no-repeat center center;
  background-size: cover;
  border: none;
  border-radius: 24rpx;
}

.used-card,
.expired-card {
  position: relative;
  margin-bottom: 30rpx;
  overflow: visible; /* 改为 visible 以显示阴影 */
  background: url('@/static/my/business/coupons_abnormal.png') no-repeat center center;
  background-size: cover;
  border: none;
  border-radius: 24rpx;
}

// 优惠券主体部分
.coupon-main {
  position: relative;
  display: flex;
  height: 160rpx;
  min-height: 160rpx;
  background: transparent;

  .coupon-divider-vertical {
    height: 100% !important;
  }
}

:deep(.wd-divider) {
  margin: 0 4rpx !important;
}

// 优惠券左侧（金额区域）
.coupon-left {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 140rpx;
  padding: 20rpx 0;
  background: transparent;
  border-radius: 24rpx;
}

// 优惠券金额样式
.coupon-amount {
  display: flex;
  align-items: baseline;
  color: #ff4545;

  .currency {
    font-size: 28rpx;
  }

  .amount {
    margin-left: 4rpx;
    font-size: 52rpx;
    font-weight: normal;
  }
}

.used-amount,
.expired-amount {
  color: #555555;
}

// 优惠券右侧（信息区域）
.coupon-right {
  display: flex;
  flex: 1;
  flex-direction: row; // 由 column 改为 row
  align-items: center; // 垂直居中
  justify-content: space-between; // 两端对齐
  padding: 20rpx 30rpx 20rpx 10rpx;
}

// 优惠券信息样式
.coupon-info {
  display: flex;
  flex: 1; // 占据剩余空间
  // 如果有多行内容，防止溢出
  flex-wrap: wrap;
  align-items: center; // 垂直居中

  .coupon-title {
    width: 100%;
    margin-bottom: 16rpx;
    font-size: 32rpx;
    font-weight: normal;
    line-height: 1.3;
    color: #ff483c;
  }

  .coupon-desc,
  .coupon-used-jobs {
    // 让这两个在一行
    display: inline-block;
    margin-right: 16rpx; // 控制间距
    font-size: 24rpx;
    font-weight: normal;
    line-height: 1.2;
    color: #888888;
  }

  .coupon-used-jobs {
    margin-right: 0;
    // 你可以单独设置样式
  }
}

.used-info,
.expired-info {
  .coupon-title {
    color: #555555;
  }
}

// 优惠券操作区域样式
.coupon-action {
  margin-top: 0; // 去掉上边距
  margin-left: 20rpx; // 可选，增加左间距
}

// 使用按钮样式
:deep(.use-btn) {
  min-width: 156rpx !important;
  height: 65rpx !important;
  padding: 8rpx 24rpx !important;
  font-size: 22rpx !important;
  font-weight: normal !important;
  color: #ffffff !important;
  background: #ff4545 !important;
  border: none !important;
  border-radius: 65rpx !important;
}

// 状态文字样式
.status-text {
  display: flex;
  align-items: center;
  justify-content: center;
  min-width: 120rpx;
  height: 50rpx;
  padding: 8rpx 20rpx;
  font-size: 20rpx;
  line-height: 34rpx;
  // background: #f0f0f0;
  color: #999999;
  text-align: center;
  border-radius: 24rpx;
}

// 有效期样式
.coupon-validity {
  position: relative;
  display: flex;
  align-items: center;
  min-height: 58rpx;
  padding: 8rpx 20rpx 8rpx 48rpx;
  overflow: visible;
  font-size: 24rpx;
  font-weight: normal;
  color: #888888;
  text-align: left;
  background: transparent;
  border-radius: 0 0 24rpx 24rpx;
  box-shadow: 0rpx 2rpx 0rpx rgba(0, 0, 0, 0.1); /* 添加底部阴影 */
}
</style>
