<route lang="json5">
{
  layout: 'default',
  style: {
    navigationBarTitleText: '',
    enablePullDownRefresh: false,
    navigationStyle: 'custom',
  },
}
</route>
<template>
  <view class="bg-img-h">
    <CustomNavBar title="个人中心" />
    <view class="pageContaner">
      <view class="form-list flex-between">
        <view class="mainText labelName">头像</view>
        <view
          v-if="!!infoObj.headImgStatus"
          class="text-32rpx font-w-500 flex-1 text-r flex justify-right items-end flex-col"
        >
          <wd-upload
            v-model:file-list="fileList"
            :limit="1"
            :upload-method="uploadMethod"
            custom-class="custom-class-img"
            image-mode="aspectFill"
            reupload
          />
        </view>
        <view v-else>
          <view class="text-32rpx font-w-500 flex-1 text-r flex justify-right items-end flex-col">
            <wd-img
              :src="infoObj.headImgUrl"
              height="100rpx"
              mode="aspectFill"
              round
              width="100rpx"
            />
          </view>

          <view class="c-#888 text-24rpx text-center">审核中</view>
        </view>
      </view>
      <view class="form-list flex-between">
        <view class="mainText labelName">姓名</view>
        <view class="text-32rpx font-w-500 flex-1 text-r">
          {{ infoObj.trueName }}
        </view>
      </view>
      <view class="form-list flex-between">
        <view class="mainText labelName">身份认证</view>
        <view v-if="infoObj.idCard" class="text-32rpx font-w-500 flex-1 text-r">
          {{ infoObj.idCard }}
        </view>
        <view
          v-else
          class="text-32rpx font-w-500 flex-1 text-r activeColor"
          @click="goIdentityAuth"
        >
          未认证
          <!-- <wd-icon name="warning" size="15px" color="#FF0000"></wd-icon> -->
        </view>
      </view>
      <view class="form-list relative">
        <view>
          <view class="flex-between">
            <view class="mainText labelName">手机号</view>
            <view class="flex justify-right items-center">
              <view v-if="infoObj.phone" class="text-32rpx font-w-500 flex-1 text-r">
                {{ infoObj.phone }}
              </view>
              <view
                v-else
                class="text-32rpx font-w-500 flex-1 text-r activeColor"
                @click="goPhoneUpdata"
              >
                未填写
              </view>
            </view>
          </view>
          <view class="flex-c p-t-10rpx">
            <wd-icon color="#999999" name="info-circle" size="14px"></wd-icon>
            <view class="subText">手机号只有在您与HR交换时候，才会告知对方</view>
          </view>
        </view>
        <!-- <view class="flex-c position-r" v-if="!infoObj.phone" @click="goPhoneUpdata">
          <view class="text-24rpx red-color">未填写,去填写</view>
          <wd-icon name="info-circle" size="14px" color="#ff0000"></wd-icon>
        </view> -->
      </view>
      <view class="form-list">
        <view class="">
          <view class="flex-between">
            <view class="mainText labelName">微信号</view>
            <view
              v-if="infoObj.wxCode"
              class="text-32rpx font-w-500 flex-1 text-r"
              @click="goWxCode"
            >
              {{ infoObj.wxCode }}
            </view>
            <view v-else class="text-32rpx font-w-500 flex-1 text-r activeColor" @click="goWxCode">
              去完善
            </view>
          </view>
          <view class="flex-c p-t-10rpx">
            <wd-icon color="#999999" name="info-circle" size="14px"></wd-icon>
            <view class="subText">微信号只有在您与HR交换时候，才会告知对方</view>
          </view>
        </view>
      </view>
      <view class="form-list">
        <view class="flex-between items-center">
          <view class="mainText labelName-1">首次参加工作时间</view>
          <view class="flex justify-right items-center">
            <wd-datetime-picker
              v-model="firstJobDate"
              :default-value="defaultTime"
              :maxDate="maxDate"
              :minDate="minDate"
              class="w-200rpx"
              custom-class="custom-class"
              custom-value-class="custom-label-class"
              placeholder="请选择"
              type="year-month"
              @confirm="confirm"
            />
          </view>
        </view>

        <view v-if="massageInfo" class="text-24rpx c-#ff0000">{{ massageInfo }}</view>
        <!-- <view class="text-32rpx font-w-500 flex-1 text-r activeColor" v-else>请选择</view> -->
      </view>

      <view class="form-list flex-between">
        <view class="mainText labelName">居住地址</view>
        <view
          v-if="infoObj.addressManager"
          class="text-32rpx font-w-500 flex-1 text-r"
          @click="goAdress"
        >
          {{ infoObj.addressManager }}
        </view>
        <view v-else class="text-32rpx font-w-500 flex-1 text-r activeColor" @click="goAdress">
          去完善
        </view>
      </view>
    </view>
  </view>
</template>

<script lang="ts" setup>
import CustomNavBar from '@/components/CustomNavBar/CustomNavBar.vue'
import { updateWorkTime, userImageAudit } from '@/interPost/my'
import { queryMyMessage } from '@/interPost/home'
import { quickHandlers } from '@/utils/compressImage'
import { formatTime, get50YearsAgoTimestamp, dateToTimestamp } from '@/utils/common'
import { uploadImgThrumPublic } from '@/service/attachment'

const { getToken } = useUserInfo()
// 获取信息
const infoObj = ref({})
const massageInfo = ref('')
const minDate = ref(null)
const maxDate = ref(null)
// 计算往前推3年的日期
const threeYearsAgo = new Date()
threeYearsAgo.setFullYear(threeYearsAgo.getFullYear() - 3)
const defaultTime = threeYearsAgo.getTime()
const firstJobDate = ref('')
const homeLocation = ref('')
const goIdentityAuth = () => {
  const idCard = infoObj.value?.idCard ? infoObj.value?.idCard : ''
  const trueName = infoObj.value?.trueName ? infoObj.value?.trueName : ''
  uni.navigateTo({
    url: `/setting/identityAuth/index?idCard=${idCard}&trueName=${trueName}`,
  })
}
// 图片id
const fileIdObj = ref({
  fileId: '',
})
const fileList = ref([])
// 工作经验
const workYear = ref(null)
// 地址
const goAdress = () => {
  const lon = infoObj.value?.lon ? infoObj.value?.lon : ''
  const lat = infoObj.value?.lat ? infoObj.value?.lat : ''
  const homeLocation = infoObj.value?.addressManager ? infoObj.value?.addressManager : ''
  uni.navigateTo({
    url: `/setting/AdressMange/index?lon=${lon}&lat=${lat}&homeLocation=${homeLocation}`,
  })
}
const uploadMethod = async (file, formData, options) => {
  const { url } = file
  try {
    const res = await uploadImgThrumPublic({
      filePath: url,
      name: options.name,
      getTask(task) {
        task.onProgressUpdate((res) => {
          options.onProgress(res, file)
        })
      },
      custom: {
        catch: true,
      },
    })
    // 调用成功回调
    if (options.onSuccess) {
      options.onSuccess(res, file, formData)
    }
    if (res.code === 0) {
      getimgInfo(res.data[0].fileId)
    } else {
      uni.showToast({
        title: '上传成功',
        icon: 'none',
        duration: 3000,
      })
    }
    return res
  } catch (err) {
    fileList.value = []
    options.onError({ errMsg: '上传失败' }, file, formData)
    throw err
  }
}

// before-upload 处理函数 - 使用预设的头像压缩配置
const beforeUpload = quickHandlers.avatar()
// 上传头像
const getimgInfo = async (fileId: any) => {
  const res: any = await userImageAudit({ fileId })
  if (res.code === 0) {
    getList()
  }
}
// 微信号
const goWxCode = () => {
  const wxCode = infoObj.value?.wxCode ? infoObj.value?.wxCode : ''
  uni.navigateTo({
    url: `/setting/wxUpdata/index?wxCode=${wxCode}`,
  })
}
// 修改手机号
const goPhoneUpdata = () => {
  const phone = infoObj.value?.phone ? infoObj.value?.phone : ''
  uni.navigateTo({
    url: `/setting/phoneUpdata/index?phone=${phone}`,
  })
}
// 获取信息
const getList = async () => {
  const res: any = await queryMyMessage()
  console.log(res, 'res====')
  if (res.code === 0) {
    infoObj.value = res.data
    if (infoObj.value?.firstJobDate) {
      firstJobDate.value = dateToTimestamp(infoObj.value?.firstJobDate)
    }
    if (infoObj.value?.headImgUrl) {
      fileList.value = [
        {
          url: infoObj.value?.headImgUrl,
          name: 'tupian',
        },
      ]
    } else {
      fileList.value = [
        {
          url:
            infoObj.value?.sex === 1
              ? '/static/header/jobhunting1.png'
              : '/static/header/jobhunting2.png',
          name: 'tupian',
        },
      ]
    }
    // const lat = res.data.location.lat
    // const lon = res.data.location.lon
    homeLocation.value = res.data.homeLocation
  }
}
onLoad(async () => {
  await uni.$onLaunched
  minDate.value = get50YearsAgoTimestamp()
  maxDate.value = new Date().getTime()
  // defaultTime.value = formatDateDay()
  // console.log(defaultTime.value, 'defaultTime.value')
})
onShow(async () => {
  await getList()
})
// 确认
const confirm = async (e) => {
  // firstJobDate.value = e.value
  const firstTime = formatTime(e.value)
  const birthday = infoObj.value?.birthday
  const firstDate = new Date(firstTime)
  const birthDate = new Date(birthday)
  let age = firstDate.getFullYear() - birthDate.getFullYear()
  const monthDiff = firstDate.getMonth() - birthDate.getMonth()
  // 如果生日还没到，年龄减1
  if (monthDiff < 0 || (monthDiff === 0 && firstDate.getDate() < birthDate.getDate())) {
    age--
  }

  if (firstDate < birthDate) {
    massageInfo.value = '参加工作时间与年龄不匹配'
  } else if (age < 16) {
    massageInfo.value = '参加工作时间与年龄不匹配'
    // console.log(`参加工作时间与年龄不匹配（工作时年龄为${age}岁，小于16岁）`)
  } else {
    massageInfo.value = ''
  }
  // console.log(firstJobDate.value, 'firstJobDate===')
  const res: any = await updateWorkTime({ firstJobDate: firstJobDate.value })
  if (res.code === 0) {
    getList()
  } else {
    uni.showToast({
      title: res.msg,
      icon: 'none',
      duration: 3000,
    })
  }
  // console.log(massageInfo.value, 'massageInfo.value===')
}
</script>

<style lang="scss" scoped>
::v-deep .custom-label-class {
  font-size: 32rpx !important;
  font-weight: 500;
}

::v-deep .wd-picker__cell {
  width: 100% !important;
  padding-top: 30rpx;
  padding-right: 0rpx !important;
  text-align: right;
  background: transparent !important;
}
:deep(.wd-cell__value--left) {
  padding-right: 0rpx !important;
  text-align: right !important;
}
:deep(.wd-cell__wrapper) {
  padding-right: 0rpx !important;
}
::v-deep .wd-upload__progress-txt {
  font-size: 20rpx !important;
}

::v-deep .wd-picker__value {
  margin-right: 0rpx;
}

::v-deep .wd-picker__arrow {
  display: none;
}

::v-deep .wd-upload__picture {
  border-radius: 50%;
}

::v-deep .wd-picker__placeholder {
  color: #4d8fff;
}

::v-deep .wd-upload {
  justify-content: right;
}

::v-deep .custom-class {
  width: 300rpx;
  height: 100rpx;
}

::v-deep .custom-class-img {
  width: 100rpx;
  height: 100rpx;
}

::v-deep .wd-upload__close {
  display: none;
}

::v-deep .wd-upload__preview {
  width: 100% !important;
  height: 100% !important;
  margin: 0rpx !important;
  border-radius: 50% !important;
}

.activeColor {
  color: #4d8fff;
}

.labelName {
  width: 200rpx;
  text-align: left;
}

.labelName-1 {
  width: 300rpx;
  text-align: left;
}

.containner-group-img {
  width: 100rpx;
  height: 100rpx;
}

.pageContaner-img {
  width: 100rpx;
  height: 100rpx;
  margin: 0 0 0 auto;
  border-radius: 50%;
}

.position-r {
  position: absolute;
  top: 10rpx;
  right: 0rpx;
}

.pageContaner {
  padding: 0rpx 40rpx 20rpx;

  .form-list {
    padding: 40rpx 0rpx;
    border-bottom: 1rpx solid #d7d6d6;

    .form-list-item {
      flex: 1;
    }

    .icon-right {
      display: flex;
      justify-content: right;
      width: 100rpx;
    }
  }
}
</style>
