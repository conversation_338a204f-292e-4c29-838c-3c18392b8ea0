<template>
  <view class="px-50rpx mt-30rpx flex flex-col gap-30rpx">
    <view
      class="bg-white shadow-[8rpx_8rpx_33rpx_0rpx_rgba(0,0,0,0.1)] rounded-20rpx overflow-hidden p-20rpx flex flex-col gap-20rpx"
    >
      <wd-textarea
        v-model="releasePostModel.positionDesc"
        :maxlength="1000"
        custom-textarea-class="h-46vh"
        fixed
        placeholder="请输入岗位职责的相关内容"
        show-word-limit
        @input="handleInput"
      />
      <view class="flex justify-end">
        <view
          class="center gap-12rpx bg-#E0ECFF rounded-30rpx h-60rpx w-180rpx"
          @tap="handleDeepseek"
        >
          <wd-img :height="18" :src="shenMaIcon" :width="18" />
          <view class="line-height-26rpx">
            <wd-img :src="shenMaName" height="14rpx" width="80rpx" />
            <view class="c-#5378FF text-20rpx p-t--10rpx">智能生成</view>
          </view>
        </view>
      </view>
    </view>
    <gao-ChatSSEClient
      ref="chatSSEClientRef"
      @onError="errorCore"
      @onFinish="finishCore"
      @onMessage="messageCore"
      @onOpen="openCore"
    />
    <wd-root-portal>
      <wd-popup
        v-model="show"
        custom-style="border-radius: 20px 20px 0px 0px;
background: linear-gradient(180deg, #7FA3F7 0%, #A8DEFF 50%, #EBF0FA 100%);padding: 40rpx 40rpx;"
        position="bottom"
      >
        <view class="">
          <view class="center gap-12rpx rounded-30rpx h-60rpx w-180rpx m-b-10rpx">
            <wd-img :height="18" :src="deepseekhost" :width="18" />
            <view class="line-height-26rpx">
              <wd-img :src="shenMaNameWhite" height="14rpx" width="80rpx" />
              <view class="c-#ffffff text-20rpx p-t--10rpx">智能生成</view>
            </view>
          </view>
          <view
            class="w-full bg-white shadow-[8rpx_8rpx_33rpx_0rpx_rgba(0,0,0,0.1)] rounded-20rpx overflow-hidden p-20rpx flex flex-col gap-20rpx"
          >
            <wd-textarea
              v-model="deepseekValue"
              :maxlength="1000"
              :placeholder="placeholder"
              custom-textarea-class="h-800rpx"
              fixed
              show-word-limit
            />
          </view>
          <view class="flex items-center gap-10rpx justify-between m-t-30rpx px-40rpx m-b-20rpx">
            <view class="flex items-center gap-10rpx" @tap="handleRefresh">
              <wd-img :src="refresh1" height="20" width="20" />
              <view :class="{ 'c-#999': !deepseekBtn }" class="c-#333 text-26rpx">重新生成</view>
            </view>
            <view class="flex items-center gap-10rpx" @tap="handleReplace">
              <wd-img :src="refresh1" height="20" width="20" />
              <view :class="{ 'c-#999': !deepseekBtn }" class="c-#333 text-26rpx">立即替换</view>
            </view>
          </view>
        </view>
      </wd-popup>
    </wd-root-portal>
  </view>
</template>

<script lang="ts" setup>
import { CommonUtil } from 'wot-design-uni'
import { useReleasePost } from '@/sub_business/hooks/useReleasePost'
import { hrCompanyWorkAddressQueryPassList } from '@/service/hrCompanyWorkAddress'
import deepseekIcon from '@/sub_business/static/release/deepseek.png'
import shenMaName from '@/sub_business/static/release/shenMaIcon_name.png'
import shenMaIcon from '@/sub_business/static/release/shenMa_icon.png'
import deepseekhost from '@/sub_business/static/release/deepseekhost.png'
import refresh1 from '@/sub_business/static/release/refresh1.png'
import shenMaNameWhite from '@/sub_business/static/release/shenMa_name_white.png'
import refresh2 from '@/sub_business/static/release/refresh2.png'

const {
  chatSSEClientRef,
  start: sseStart,
  stop: sseStop,
  openCore,
  errorCore,
  messageCore: sseMessageCore,
} = useDeepseeksse()
// 按钮是否显示
const deepseekBtn = ref(true)
// 是否显示深度搜索
const show = ref(false)
const deepseekValue = ref('')
const positionName = ref('')
const placeholder = ref('请输入岗位职责的相关内容')
const { releasePostModel, releaseActiveAddress } = useReleasePost()

async function fetchHrCompanyWorkAddressQueryPassList() {
  const { data } = await hrCompanyWorkAddressQueryPassList({
    entity: {},
    page: 1,
    size: 1,
  })
  const [first] = data.list
  if (first?.id) {
    releaseActiveAddress.value = first
    releasePostModel.value.companyWorkAddressId = first.id
  }
}

const finishCore = () => {
  deepseekBtn.value = true
  console.log('finishCore===============================')
}
// 封装 messageCore
const messageCore = (msg) => {
  deepseekBtn.value = false
  sseMessageCore(msg)
  deepseekValue.value += `${msg.data.replace(/(?:\*){1,2}/g, '')}`
}
// 重新生成
const handleRefresh = () => {
  if (!deepseekBtn.value) return
  const msg = deepseekValue.value
  deepseekValue.value = ''
  placeholder.value = '思考中...'
  sseStart('/easyzhipin-ai/aliChat/positionDesc', '', positionName.value, 250)
}
// 立即替换
const handleReplace = () => {
  if (!deepseekBtn.value) return
  releasePostModel.value.positionDesc = deepseekValue.value
  show.value = false
}
const handleInput = ({ value }) => {
  deepseekValue.value = value
}

function handleSelectWorkerAddress() {
  uni.navigateTo({
    url: CommonUtil.buildUrlWithParams('/sub_business/pages/AddressCenter/index', {
      source: 'release',
    }),
  })
}

// 深度搜索
function handleDeepseek() {
  show.value = true
  if (!deepseekValue.value.length) {
    setTimeout(() => {
      sseStart('/easyzhipin-ai/aliChat/positionDesc', '', positionName.value, 250)
    }, 100)
  }
}

const handleConfirm = () => {
  if (!releasePostModel.value.positionDesc) {
    uni.showToast({
      title: '请填写岗位描述',
      icon: 'none',
      mask: true,
    })
    return Promise.reject(new Error('岗位描述不能为空'))
  }
  return Promise.resolve()
}
onMounted(() => {
  positionName.value = releasePostModel.value.positionMarkName
    ? releasePostModel.value.positionMarkName
    : releasePostModel.value.positionName
  fetchHrCompanyWorkAddressQueryPassList()
})
defineExpose({
  submitData: handleConfirm,
})
</script>

<style lang="scss" scoped></style>
