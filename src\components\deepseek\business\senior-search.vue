<template>
  <z-paging :fixed="false" layout-only safe-area-inset-bottom>
    <wd-config-provider :themeVars="themeVars">
      <view class="px-32rpx pb-40rpx flex flex-col gap-56rpx">
        <view class="flex flex-col gap-44rpx">
          <view
            class="rounded-38rpx h-76rpx w-344rpx bg-#2F2F2F px-36rpx flex items-center"
            @click="handleSelectPost"
          >
            <text class="c-#E4CC9C text-28rpx flex-1 line-clamp-1">
              {{
                releaseSeniorPostActivePost?.positionMarkName
                  ? releaseSeniorPostActivePost?.positionMarkName
                  : (releaseSeniorPostActivePost?.positionName ?? '暂未发布岗位')
              }}
            </text>
            <text
              :class="modelShow ? 'i-carbon-triangle-solid' : 'i-carbon-triangle-down-solid'"
              class="text-16rpx c-#E4CC9C"
            />
          </view>
          <!-- <view class="flex items-center flex-wrap gap-x-34rpx gap-y-24rpx">
            <view
              v-for="(item, key) in releaseActivePost.positionKeyList"
              :key="`keywords-${key}`"
              class="h-76rpx rounded-60rpx border-1px border-solid border-#9F9F9F center px-30rpx"
            >
              <text class="text-24rpx c-#D3D3D3">{{ item }}</text>
            </view>
          </view> -->
          <view class="flex flex-col gap-44rpx">
            <text class="c-#E4CC9C text-36rpx font-500">期望薪资</text>
            <view class="h-132rpx rounded-38rpx bg-#2F2F2F flex items-center">
              <wd-picker
                :key="salaryPickerKey"
                ref="pickerPop"
                v-model="salaryValue"
                :column-change="onSalaryColumnChange"
                :columns="salaryColumns"
                :display-format="salaryDisplayFormat"
                custom-class="custom-class"
                placeholder="最低 - 最高"
                @confirm="handleSalaryConfirm"
              />
            </view>
          </view>
          <view class="flex flex-col gap-44rpx">
            <text class="c-#E4CC9C text-36rpx font-500">求职类型</text>
            <view class="flex items-center flex-wrap gap-x-32rpx gap-y-24rpx">
              <view
                v-for="(item, key) in seekTypeOptions"
                :key="`seek-${key}`"
                :class="{ 'border-#FFCB62': seniorPostModel.jobType === item.value }"
                class="h-76rpx rounded-60rpx border-1px border-solid border-#9F9F9F center px-42rpx"
                @tap="handleSelectSeekType(item)"
              >
                <text
                  :class="{ 'c-#FFCB62': seniorPostModel.jobType === item.value }"
                  class="text-24rpx c-#D3D3D3"
                >
                  {{ item.text }}
                </text>
              </view>
            </view>
          </view>
          <view class="flex flex-col gap-44rpx">
            <text class="c-#E4CC9C text-36rpx font-500">求职状态</text>
            <view class="flex items-center flex-wrap gap-x-18rpx gap-y-24rpx">
              <view
                v-for="(item, key) in seekStatusOptions"
                :key="`seek-${key}`"
                :class="{ 'border-#FFCB62': seniorPostModel.seekStatus === item.value }"
                class="h-76rpx rounded-60rpx border-1px border-solid border-#9F9F9F center px-25rpx"
                @tap="handleSelectSeekStatus(item)"
              >
                <text
                  :class="{ 'c-#FFCB62': seniorPostModel.seekStatus === item.value }"
                  class="text-24rpx c-#D3D3D3"
                >
                  {{ item.text }}
                </text>
              </view>
            </view>
          </view>
          <view class="flex flex-col gap-44rpx">
            <text class="c-#E4CC9C text-36rpx font-500">年龄要求</text>
            <view class="h-132rpx rounded-38rpx bg-#2F2F2F flex items-center">
              <wd-picker
                :key="agePickerKey"
                ref="agePickerPop"
                v-model="ageValue"
                :column-change="onAgeColumnChange"
                :columns="ageColumns"
                :display-format="ageDisplayFormat"
                placeholder="最低 - 最高"
                @cancel="handleAgeCancel"
                @close="handleAgeClose"
                @confirm="handleAgeConfirm"
              />
            </view>
          </view>
          <view class="flex flex-col gap-44rpx">
            <text class="c-#E4CC9C text-36rpx font-500">性别</text>
            <view class="grid grid-cols-3 gap-34rpx">
              <view
                v-for="(item, key) in genderOptions"
                :key="`gender-${key}`"
                :class="{ 'border-#FFCB62': seniorPostModel.gender === item.value }"
                class="h-76rpx rounded-60rpx border-1px border-solid border-#9F9F9F center px-30rpx"
                @tap="handleSelectGender(item)"
              >
                <text
                  :class="{ 'c-#FFCB62': seniorPostModel.gender === item.value }"
                  class="text-24rpx c-#D3D3D3"
                >
                  {{ item.text }}
                </text>
              </view>
            </view>
          </view>
          <view class="flex flex-col gap-44rpx">
            <text class="c-#E4CC9C text-36rpx font-500">学历要求</text>
            <view class="flex items-center flex-wrap gap-x-34rpx gap-y-24rpx">
              <view
                v-for="(item, key) in qualificationOptions"
                :key="`qualification-${key}`"
                :class="{ 'border-#FFCB62': seniorPostModel.qualification === item.value }"
                class="h-76rpx min-w-200rpx rounded-60rpx border-1px border-solid border-#9F9F9F center px-30rpx"
                @tap="handleSelectQualification(item)"
              >
                <text
                  :class="{ 'c-#FFCB62': seniorPostModel.qualification === item.value }"
                  class="text-24rpx c-#D3D3D3"
                >
                  {{ item.text }}
                </text>
              </view>
            </view>
          </view>
        </view>
      </view>
    </wd-config-provider>
    <template #bottom>
      <view class="p-[36rpx_30rpx]">
        <wd-button
          :round="false"
          custom-class="!bg-#2F2F2F !h-112rpx w-full"
          @click="handleConfirm"
        >
          <text class="c-#E4CC9C text-28rpx">保存并筛选</text>
        </wd-button>
      </view>
    </template>
  </z-paging>
</template>

<script lang="ts" setup>
import { DICT_IDS, Gender, SALARY_RANGE } from '@/enum'
import { useReleasePost } from '@/hooks/business/useReleasePost'
import type { DictOption } from '@/hooks/common/useDictionary'
import type { ConfigProviderThemeVars } from 'wot-design-uni'
import type { hrResumeSeniorPostModelInt } from '@/service/hrResume/types'

const $emit = defineEmits<{
  (e: 'search', model: hrResumeSeniorPostModelInt): void
  (e: 'selectPost'): void
}>()

// 接收初始数据用于回显
const props = defineProps<{
  initialData?: hrResumeSeniorPostModelInt
}>()

const modelShow = defineModel('show', {
  type: Boolean,
  default: false,
})
const { getDictOptions } = useDictionary()
const { releaseSeniorPostActivePost } = useReleasePost()

// picker 组件引用
const pickerPop = ref()
const agePickerPop = ref()

// 用于强制重新渲染 picker 的 key
const salaryPickerKey = ref(0)
const agePickerKey = ref(0)

// 监听职位变化，当选择新职位时关闭下拉框
watch(
  () => releaseSeniorPostActivePost.value.positionName,
  (newPositionName) => {
    if (newPositionName && modelShow.value) {
      modelShow.value = false
    }
  },
)
const themeVars: ConfigProviderThemeVars = {
  inputPlaceholderColor: '#FFCB62',
  inputColor: '#FFCB62',
  inputBg: 'transparent',
}
const seniorPostModel = ref<hrResumeSeniorPostModelInt>({})
const qualificationOptions = ref<DictOption[]>([])
const seekStatusOptions = ref<DictOption[]>([])
const seekTypeOptions = ref<DictOption[]>([])
const genderOptions = ref<DictOption[]>([
  { value: Gender.UNSET, text: '不限' },
  { value: Gender.MALE, text: '男' },
  { value: Gender.FEMALE, text: '女' },
])

const salaryExpectationStart = ref<any>('') // 薪资开始
const salaryExpectationEnd = ref<any>('') // 薪资结束

const ageBegin = ref<any>('') // 年龄开始
const ageEnd = ref<any>('') // 年龄结束

// 预计算薪资数据，避免重复计算
const salaryColumnsData = computed(() => [
  Object.keys(SALARY_RANGE).map((item) => ({ label: item, value: item })),
  SALARY_RANGE['面议'].map((item) => ({ label: item, value: item })),
])

const salaryColumns = ref(salaryColumnsData.value)
const salaryValue = ref([])

const ageOptionsCache = (() => {
  const baseOptions = [
    { label: '不限', value: '不限' },
    ...Array.from({ length: 49 }, (_, i) => ({ label: `${i + 16}岁`, value: i + 16 })),
  ]

  const rightBaseOptions = [
    { label: '不限', value: '不限' },
    ...Array.from({ length: 50 }, (_, i) => ({ label: `${i + 16}岁`, value: i + 16 })),
  ]

  const ageRangeCache = new Map()
  for (let startAge = 16; startAge <= 64; startAge++) {
    const maxEndAge = 65
    const endOptions = Array.from({ length: maxEndAge - startAge }, (_, i) => ({
      label: `${startAge + i + 1}岁`,
      value: startAge + i + 1,
    }))
    ageRangeCache.set(startAge, endOptions)
  }

  return { baseOptions, rightBaseOptions, ageRangeCache }
})()

// 默认情况下，左边是"不限"，右边也只显示"不限"
const ageColumns = ref([ageOptionsCache.baseOptions, [{ label: '不限', value: '不限' }]])

const ageValue = ref([])

// 移除 salaryDataCache，直接使用 SALARY_RANGE

const onSalaryColumnChange = (picker: any, values: any, columnIndex: any, resolve: any) => {
  if (columnIndex === 0) {
    const selected = values[0]?.value || '面议'

    if (selected === '面议') {
      picker.setColumnData(1, [{ label: '面议', value: '面议' }])
    } else {
      // 直接从 SALARY_RANGE 获取数据
      const secondColumnData = SALARY_RANGE[selected]
        ? SALARY_RANGE[selected].map((item: string) => ({ label: item, value: item }))
        : [{ label: '面议', value: '面议' }]
      picker.setColumnData(1, secondColumnData)
    }
  }
  // 无论哪一列变化都要调用resolve()
  resolve()
}

const salaryDisplayFormat = (items: any) => {
  if (items[0].label === '面议' && items[1].label === '面议') {
    return '面议'
  }
  return items.map((item: any) => item.label).join(' - ')
}

const onAgeColumnChange = (picker: any, values: any, columnIndex: any, resolve: any) => {
  if (columnIndex === 0) {
    const selectedAge = values[0]?.value

    if (selectedAge === '不限') {
      // 当左边选择"不限"时，右边只显示"不限"
      picker.setColumnData(1, [{ label: '不限', value: '不限' }])
    } else {
      const startAge = typeof selectedAge === 'number' ? selectedAge : 16
      const cachedEndOptions = ageOptionsCache.ageRangeCache.get(startAge)

      if (cachedEndOptions) {
        picker.setColumnData(1, cachedEndOptions)
      } else {
        picker.setColumnData(1, ageOptionsCache.baseOptions)
      }
    }
  }
  // 无论哪一列变化都要调用resolve()
  resolve()
}

const ageDisplayFormat = (items: any) => {
  if (items[0].label === '不限') {
    // 当左边是"不限"时，统一显示为"不限"（因为右边也必须是"不限"）
    return '不限'
  }
  if (items[1].label === '不限') {
    return `${items[0].label}-不限`
  }
  return items.map((item: any) => item.label).join(' - ')
}

const handleSalaryConfirm = ({ value }) => {
  if (value[0] === '面议' && value[1] === '面议') {
    // 面议情况，设置为0
    salaryExpectationStart.value = 0
    salaryExpectationEnd.value = 0
    seniorPostModel.value.salaryExpectationStart = 0
    seniorPostModel.value.salaryExpectationEnd = 0
  } else if (value[0].indexOf('k') !== -1) {
    // 正常薪资范围
    salaryExpectationStart.value = value[0].replace('k', '000')
    salaryExpectationEnd.value = value[1].replace('k', '000')
    seniorPostModel.value.salaryExpectationStart = salaryExpectationStart.value
    seniorPostModel.value.salaryExpectationEnd = salaryExpectationEnd.value
  } else {
    // 其他情况，设置为0
    salaryExpectationStart.value = 0
    salaryExpectationEnd.value = 0
    seniorPostModel.value.salaryExpectationStart = 0
    seniorPostModel.value.salaryExpectationEnd = 0
  }

  // 确认后重新设置列数据，确保下次打开时数据正确
  setTimeout(() => {
    setSalaryDisplay(
      seniorPostModel.value.salaryExpectationStart || 0,
      seniorPostModel.value.salaryExpectationEnd || 0,
    )
    // 通过改变 key 强制重新渲染 picker 组件
    salaryPickerKey.value++
  }, 100)
}

// 处理年龄确认事件
const handleAgeConfirm = ({ value }) => {
  if (value[0] === '不限') {
    // 当左边选择"不限"时，右边必须也是"不限"
    ageBegin.value = 0
    ageEnd.value = 0
    seniorPostModel.value.ageBegin = 0
    seniorPostModel.value.ageEnd = 0
  } else if (value[1] === '不限') {
    // 结束不限，起始有值
    ageBegin.value = value[0]
    ageEnd.value = 0
    seniorPostModel.value.ageBegin = value[0]
    seniorPostModel.value.ageEnd = 0
  } else {
    // 正常年龄范围
    ageBegin.value = value[0]
    ageEnd.value = value[1]
    seniorPostModel.value.ageBegin = value[0]
    seniorPostModel.value.ageEnd = value[1]
  }

  console.log(ageBegin.value, ageEnd.value, 'age====')

  // 确认后重新设置列数据，确保下次打开时数据正确
  setTimeout(() => {
    setAgeDisplay(seniorPostModel.value.ageBegin || 0, seniorPostModel.value.ageEnd || 0)
    // 通过改变 key 强制重新渲染 picker 组件
    agePickerKey.value++
  }, 100)
}

// 回显薪资数据
const setSalaryDisplay = (startSalary: number, endSalary: number) => {
  console.log('setSalaryDisplay called with:', startSalary, endSalary)

  if (startSalary === 0 && endSalary === 0) {
    // 面议情况
    salaryValue.value = ['面议', '面议']
    // 确保第二列数据正确
    salaryColumns.value = [
      Object.keys(SALARY_RANGE).map((item) => ({ label: item, value: item })),
      [{ label: '面议', value: '面议' }],
    ]
  } else if (startSalary > 0 && endSalary > 0) {
    // 正常薪资范围，转换为k单位显示
    const startK = Math.floor(startSalary / 1000) + 'k'
    const endK = Math.floor(endSalary / 1000) + 'k'
    console.log('Setting salary values:', startK, endK)

    // 先设置第一列数据
    const firstColumnData = Object.keys(SALARY_RANGE).map((item) => ({ label: item, value: item }))

    // 根据第一列的值获取第二列数据
    const secondColumnData = SALARY_RANGE[startK]
      ? SALARY_RANGE[startK].map((item: string) => ({ label: item, value: item }))
      : [{ label: '面议', value: '面议' }]

    console.log('Second column data for', startK, ':', secondColumnData)

    // 设置列数据
    salaryColumns.value = [firstColumnData, secondColumnData]

    // 设置选中值
    salaryValue.value = [startK, endK]

    console.log('Final salaryColumns:', salaryColumns.value)
    console.log('Final salaryValue:', salaryValue.value)
  } else {
    // 默认值
    salaryValue.value = []
    // 重置为默认列数据
    salaryColumns.value = [
      Object.keys(SALARY_RANGE).map((item) => ({ label: item, value: item })),
      SALARY_RANGE['面议'].map((item) => ({ label: item, value: item })),
    ]
  }
}

// 回显年龄数据
const setAgeDisplay = (startAge: number, endAge: number) => {
  if (startAge === 0 && endAge === 0) {
    // 不限情况
    ageValue.value = ['不限', '不限']
    // 当左边是"不限"时，右边只显示"不限"
    ageColumns.value = [ageOptionsCache.baseOptions, [{ label: '不限', value: '不限' }]]
  } else if (startAge === 0 && endAge > 0) {
    // 起始不限，但这种情况在逻辑上不应该存在，因为左边不限时右边只能是不限
    // 为了数据一致性，强制设置为双不限
    ageValue.value = ['不限', '不限']
    ageColumns.value = [ageOptionsCache.baseOptions, [{ label: '不限', value: '不限' }]]
  } else if (startAge > 0 && endAge === 0) {
    // 结束不限
    ageValue.value = [startAge, '不限']
    // 根据起始年龄更新第二列数据
    const cachedEndOptions = ageOptionsCache.ageRangeCache.get(startAge)
    if (cachedEndOptions) {
      ageColumns.value = [ageOptionsCache.baseOptions, cachedEndOptions]
    } else {
      ageColumns.value = [ageOptionsCache.baseOptions, ageOptionsCache.baseOptions]
    }
  } else if (startAge > 0 && endAge > 0) {
    // 正常年龄范围
    ageValue.value = [startAge, endAge]
    // 根据起始年龄更新第二列数据
    const cachedEndOptions = ageOptionsCache.ageRangeCache.get(startAge)
    if (cachedEndOptions) {
      ageColumns.value = [ageOptionsCache.baseOptions, cachedEndOptions]
    } else {
      ageColumns.value = [ageOptionsCache.baseOptions, ageOptionsCache.baseOptions]
    }
  } else {
    // 默认值
    ageValue.value = []
    // 重置为默认列数据：左边是完整选项，右边默认只显示"不限"
    ageColumns.value = [ageOptionsCache.baseOptions, [{ label: '不限', value: '不限' }]]
  }
}

// 初始化回显数据
const initDisplayData = (model: hrResumeSeniorPostModelInt) => {
  // 使用 nextTick 确保 DOM 更新完成后再设置回显数据
  nextTick(() => {
    if (model.salaryExpectationStart !== undefined && model.salaryExpectationEnd !== undefined) {
      setSalaryDisplay(model.salaryExpectationStart, model.salaryExpectationEnd)
    }
    if (model.ageBegin !== undefined && model.ageEnd !== undefined) {
      setAgeDisplay(model.ageBegin, model.ageEnd)
    }
  })
}

const handleAgeCancel = () => {
  console.log('年龄选择器已取消')
}

const handleAgeClose = () => {
  console.log('年龄选择器已关闭')
}

const fetchWorkEducationalOptions = async () => {
  const dictData = await getDictOptions(DICT_IDS.EDUCATION_B)
  qualificationOptions.value = dictData
}
const fetchWorkStatusOptions = async () => {
  const dictData = await getDictOptions(DICT_IDS.SEEK_STATUS)
  seekStatusOptions.value = dictData
}

const fetchWorkTypeOptions = async () => {
  const dictData = await getDictOptions(DICT_IDS.SEEK_TYPE)
  seekTypeOptions.value = dictData
}

function handleSelectQualification(item: DictOption) {
  seniorPostModel.value.qualification =
    seniorPostModel.value.qualification === item.value ? undefined : (item.value as number)
}

function handleSelectGender(item: DictOption) {
  seniorPostModel.value.gender =
    seniorPostModel.value.gender === item.value ? undefined : (item.value as number)
}

function handleSelectSeekStatus(item: DictOption) {
  seniorPostModel.value.seekStatus =
    seniorPostModel.value.seekStatus === item.value ? undefined : (item.value as number)
}

function handleSelectSeekType(item: DictOption) {
  seniorPostModel.value.jobType =
    seniorPostModel.value.jobType === item.value ? undefined : (item.value as number)
}

function handleSelectPost() {
  // 切换下拉框显示状态
  modelShow.value = !modelShow.value
  // 发出事件通知父组件显示职位选择弹出框
  $emit('selectPost')
}

function handleConfirm() {
  const filteredModel = Object.fromEntries(
    Object.entries(seniorPostModel.value).filter(
      ([_, value]) =>
        value !== undefined && value !== null && !(typeof value === 'string' && value === ''),
    ),
  )
  const isHaveModel = Object.keys(filteredModel).length
  if (!isHaveModel) {
    uni.showToast({
      title: '请先选择筛选条件',
      icon: 'none',
    })
    return
  }

  // 薪资验证：只有当两个值都不为0时才进行大小比较
  if (
    seniorPostModel.value.salaryExpectationStart !== 0 &&
    seniorPostModel.value.salaryExpectationEnd !== 0
  ) {
    const startSalary = Number(seniorPostModel.value.salaryExpectationStart)
    const endSalary = Number(seniorPostModel.value.salaryExpectationEnd)
    if (startSalary >= endSalary) {
      uni.showToast({
        title: '开始薪资必须小于结束薪资',
        icon: 'none',
      })
      return
    }
  }

  // 年龄验证：只有当两个值都不为0时才进行大小比较
  if (seniorPostModel.value.ageBegin !== 0 && seniorPostModel.value.ageEnd !== 0) {
    const startAge = Number(seniorPostModel.value.ageBegin)
    const endAge = Number(seniorPostModel.value.ageEnd)
    if (startAge >= endAge) {
      uni.showToast({
        title: '起始年龄必须小于截止年龄',
        icon: 'none',
      })
      return
    }
  }

  $emit('search', filteredModel)
}

onMounted(async () => {
  await uni.$onLaunched
  await fetchWorkEducationalOptions()
  await fetchWorkStatusOptions()
  await fetchWorkTypeOptions()

  // 如果有初始数据，进行回显处理
  if (props.initialData) {
    // 将初始数据合并到模型中
    Object.assign(seniorPostModel.value, props.initialData)
    // 设置显示值
    initDisplayData(props.initialData)
  }
})
</script>

<style lang="scss" scoped>
:deep(.wd-input) {
  .wd-input__placeholder {
    text-align: center;
  }
}

:deep(.zp-view-super) {
  margin: 0 !important;
}

:deep(.zp-paging-container-content) {
  height: auto !important;
}

:deep(.wd-transition) {
  &.wd-slide-up-leave-to,
  &.wd-fade-leave-to {
    display: none !important;
    pointer-events: none !important;
    visibility: hidden !important;
    opacity: 0 !important;
  }
}

:deep(.wd-picker) {
  width: 100%;
  height: 100%;

  .wd-cell {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 100%;
    background: transparent;

    .wd-cell__value {
      display: flex;
      flex: 1;
      align-items: center;
      justify-content: center;
      font-size: 32rpx;
      font-weight: normal !important;
      color: #ffcb62;
    }

    .wd-icon {
      display: none;
    }
  }

  .wd-picker__field {
    height: 100%;

    .wd-picker__cell {
      width: 100%;
      height: 100%;
      background: transparent;

      .wd-picker__body {
        height: 100%;

        .wd-picker__value-wraper {
          height: 100%;
        }
      }
    }
  }

  // 优化picker滚动性能
  .wd-picker-view {
    transform: translateZ(0);
    backface-visibility: hidden;
    -webkit-overflow-scrolling: touch;
    will-change: transform;
  }

  .wd-picker-view-column {
    transform: translateZ(0);
    backface-visibility: hidden;
  }

  .wd-picker-view-item {
    transform: translateZ(0);
    backface-visibility: hidden;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
  }
}

:deep(.wd-picker__wraper) {
  background: #383838;
}

:deep(.wd-picker-view-column__item) {
  color: #d3d3d3;
  background: #383838;
}

:deep(.wd-picker-view-column__item--active) {
  color: #ffcb62;
}

:deep(.uni-picker-view-content) {
  background: #383838;
}

:deep(.wd-picker__action) {
  color: #ffcb62 !important;
}

:deep(.wd-picker__action--cancel) {
  color: #d3d3d3 !important;
}
</style>
