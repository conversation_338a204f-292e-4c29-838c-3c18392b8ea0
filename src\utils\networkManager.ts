class NetworkManager {
  private isOnline: boolean = true
  private pendingTasks: (() => Promise<void>)[] = []
  private retryTasks: (() => Promise<void>)[] = []
  private isListening: boolean = false
  private networkStatusCallback: ((res: any) => void) | null = null

  /**
   * 开始监听网络状态
   */
  startListening() {
    if (this.isListening) return
    this.isListening = true
    this.networkStatusCallback = (res) => {
      console.log('网络状态变化:', res)
      const wasOffline = !this.isOnline
      this.isOnline = res.isConnected

      if (wasOffline && this.isOnline) {
        console.log('网络已连接，执行待处理任务')
        this.executePendingTasks()
        this.executeRetryTasks()
      } else if (!this.isOnline) {
        console.log('网络已断开')
      }
    }
    uni.onNetworkStatusChange(this.networkStatusCallback)
    uni.getNetworkType({
      success: (res) => {
        this.isOnline = res.networkType !== 'none'
        console.log('当前网络状态:', res.networkType, '是否在线:', this.isOnline)
      },
    })
  }

  /**
   * 停止监听网络状态
   */
  stopListening() {
    if (!this.isListening || !this.networkStatusCallback) return
    this.isListening = false
    uni.offNetworkStatusChange(this.networkStatusCallback)
    this.networkStatusCallback = null
  }

  /**
   * 添加待执行任务（网络连接后执行）
   */
  addPendingTask(task: () => Promise<void>) {
    if (this.isOnline) {
      task().catch((error) => {
        console.error('任务执行失败:', error)
        this.retryTasks.push(task)
      })
    } else {
      this.pendingTasks.push(task)
    }
  }

  /**
   * 添加重试任务
   */
  addRetryTask(task: () => Promise<void>) {
    this.retryTasks.push(task)
  }

  /**
   * 执行待处理任务
   */
  private async executePendingTasks() {
    if (this.pendingTasks.length === 0) return

    const tasks = [...this.pendingTasks]
    this.pendingTasks = []

    for (const task of tasks) {
      try {
        await task()
      } catch (error) {
        console.error('待处理任务执行失败:', error)
        this.retryTasks.push(task)
      }
    }
  }

  /**
   * 执行重试任务
   */
  private async executeRetryTasks() {
    if (this.retryTasks.length === 0) return

    const tasks = [...this.retryTasks]
    this.retryTasks = []

    for (const task of tasks) {
      try {
        await task()
      } catch (error) {
        console.error('重试任务执行失败:', error)
        this.retryTasks.push(task)
      }
    }
  }

  /**
   * 检查网络状态
   */
  checkNetworkStatus(): Promise<boolean> {
    return new Promise((resolve) => {
      uni.getNetworkType({
        success: (res) => {
          const isOnline = res.networkType !== 'none'
          this.isOnline = isOnline
          resolve(isOnline)
        },
        fail: () => {
          resolve(false)
        },
      })
    })
  }

  /**
   * 获取当前网络状态
   */
  get isNetworkOnline(): boolean {
    return this.isOnline
  }
}

export const networkManager = new NetworkManager()
export default networkManager
