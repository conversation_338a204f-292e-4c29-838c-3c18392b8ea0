{"globalStyle": {"navigationStyle": "default", "navigationBarTitleText": "易直聘", "navigationBarBackgroundColor": "#f8f8f8", "navigationBarTextStyle": "black", "backgroundColor": "#FFFFFF", "app-plus": {"bounce": "none", "scrollIndicator": "none"}}, "easycom": {"autoscan": true, "custom": {"^wd-(.*)": "wot-design-uni/components/wd-$1/wd-$1.vue", "^(?!z-paging-refresh|z-paging-load-more)z-paging(.*)": "z-paging/components/z-paging$1/z-paging$1.vue"}}, "preloadRule": {}, "tabBar": {"custom": true, "color": "#999999", "selectedColor": "#018d71", "list": [{"pagePath": "pages/home/<USER>"}, {"pagePath": "pages/deepseek/index"}, {"pagePath": "pages/news/index"}, {"pagePath": "pages/mine/index"}]}, "subPackages": [{"root": "loginSetting", "pages": [{"path": "accountLogin/index", "type": "page", "layout": "default", "style": {"navigationBarTitleText": "", "enablePullDownRefresh": false, "navigationStyle": "custom"}}, {"path": "category/career", "type": "page", "layout": "default", "style": {"navigationBarTitleText": "", "enablePullDownRefresh": false, "navigationStyle": "custom"}}, {"path": "category/expPosition", "type": "page", "layout": "default", "style": {"navigationBarTitleText": "", "enablePullDownRefresh": false, "navigationStyle": "custom"}}, {"path": "category/index", "type": "page", "layout": "default", "style": {"navigationBarTitleText": "", "enablePullDownRefresh": false, "navigationStyle": "custom"}}, {"path": "category/JobIntention", "type": "page", "layout": "default", "style": {"navigationBarTitleText": "", "enablePullDownRefresh": false, "navigationStyle": "custom"}}, {"path": "category/region", "type": "page", "layout": "default", "style": {"navigationBarTitleText": "", "enablePullDownRefresh": false, "navigationStyle": "custom"}}, {"path": "companyJoin/companyInfo", "type": "page", "layout": "default", "style": {"navigationBarTitleText": "", "enablePullDownRefresh": false, "navigationStyle": "custom"}}, {"path": "companyJoin/jobCertificate", "type": "page", "layout": "default", "style": {"navigationBarTitleText": "", "enablePullDownRefresh": false, "navigationStyle": "custom"}}, {"path": "companyJoin/recrIdent", "type": "page", "layout": "default", "style": {"navigationBarTitleText": "", "enablePullDownRefresh": false, "navigationStyle": "custom"}}, {"path": "createResume/biographicalOne", "type": "page", "layout": "default", "style": {"navigationBarTitleText": "", "enablePullDownRefresh": false, "navigationStyle": "custom", "app-plus": {"softinputMode": "adjustResize"}}}, {"path": "createResume/biographicalTwo", "type": "page", "layout": "default", "style": {"navigationBarTitleText": "", "enablePullDownRefresh": false, "navigationStyle": "custom"}}, {"path": "CustomerService/index", "type": "page", "layout": "default", "style": {"navigationBarTitleText": "", "enablePullDownRefresh": false, "navigationStyle": "custom"}}, {"path": "Externalfiling/index", "type": "page", "layout": "default", "style": {"navigationBarTitleText": "", "enablePullDownRefresh": false, "navigationStyle": "custom"}}, {"path": "regPage/index", "type": "page", "layout": "default", "style": {"navigationBarTitleText": "", "enablePullDownRefresh": false, "navigationStyle": "custom"}}, {"path": "verifiCode/index", "type": "page", "layout": "default", "style": {"navigationBarTitleText": "", "enablePullDownRefresh": false, "navigationStyle": "custom"}}]}, {"root": "chartPage", "pages": [{"path": "chartPage/index", "type": "page", "layout": "default", "style": {"navigationBarTitleText": "", "enablePullDownRefresh": false, "navigationStyle": "custom"}}, {"path": "message/index", "type": "page", "layout": "default", "style": {"navigationBarTitleText": "", "enablePullDownRefresh": false, "navigationStyle": "custom"}}, {"path": "message/infoDetail", "type": "page", "layout": "default", "style": {"navigationBarTitleText": "", "enablePullDownRefresh": false, "navigationStyle": "custom"}}, {"path": "message/messageActiveList", "type": "page", "layout": "default", "style": {"navigationBarTitleText": "", "enablePullDownRefresh": false, "navigationStyle": "custom"}}, {"path": "message/messageSysList", "type": "page", "layout": "default", "style": {"navigationBarTitleText": "", "enablePullDownRefresh": false, "navigationStyle": "custom"}}, {"path": "setUp/index", "type": "page", "layout": "default", "style": {"navigationBarTitleText": "", "enablePullDownRefresh": false, "navigationStyle": "custom"}}]}, {"root": "resumeRelated", "pages": [{"path": "AttachmentResume/GeneratePDF", "type": "page", "layout": "default", "style": {"navigationBarTitleText": "", "enablePullDownRefresh": false, "navigationStyle": "custom"}}, {"path": "AttachmentResume/index", "type": "page", "layout": "default", "style": {"navigationBarTitleText": "", "enablePullDownRefresh": false, "navigationStyle": "custom"}}, {"path": "AttachmentResume/WebViewpdf", "type": "page", "layout": "default", "style": {"navigationBarTitleText": "", "enablePullDownRefresh": false, "navigationStyle": "custom"}}, {"path": "AttachmentResume/WebViewUpload", "type": "page", "layout": "default", "style": {"navigationBarTitleText": "", "enablePullDownRefresh": false, "navigationStyle": "custom"}}, {"path": "collect/index", "type": "page", "layout": "default", "style": {"navigationBarTitleText": "", "enablePullDownRefresh": false, "navigationStyle": "custom"}}, {"path": "collectResume/index", "type": "page", "layout": "default", "style": {"navigationBarTitleText": "", "enablePullDownRefresh": false, "navigationStyle": "custom"}}, {"path": "communicate/index", "type": "page", "layout": "default", "style": {"navigationBarTitleText": "", "enablePullDownRefresh": false, "navigationStyle": "custom"}}, {"path": "company/index", "type": "page", "layout": "default", "style": {"navigationBarTitleText": "", "enablePullDownRefresh": false, "navigationStyle": "custom"}}, {"path": "component/interviewCard", "type": "page"}, {"path": "corporateName/index", "type": "page", "layout": "default", "style": {"navigationBarTitleText": "", "enablePullDownRefresh": false, "navigationStyle": "custom"}}, {"path": "CustomerService/index", "type": "page", "layout": "default", "style": {"navigationBarTitleText": "", "enablePullDownRefresh": false, "navigationStyle": "custom"}}, {"path": "education/index", "type": "page", "layout": "default", "style": {"navigationBarTitleText": "", "enablePullDownRefresh": false, "navigationStyle": "custom"}}, {"path": "education/major", "type": "page", "layout": "default", "style": {"navigationBarTitleText": "", "enablePullDownRefresh": false, "navigationStyle": "custom"}}, {"path": "education/school", "type": "page", "layout": "default", "style": {"navigationBarTitleText": "", "enablePullDownRefresh": false, "navigationStyle": "custom"}}, {"path": "filter/index", "type": "page", "layout": "default", "style": {"navigationBarTitleText": "", "enablePullDownRefresh": false, "navigationStyle": "custom"}}, {"path": "HomeRegion/index", "type": "page", "layout": "default", "style": {"navigationBarTitleText": "", "enablePullDownRefresh": false, "navigationStyle": "custom"}}, {"path": "HomeSearch/index", "type": "page", "layout": "default", "style": {"navigationBarTitleText": "", "enablePullDownRefresh": false, "navigationStyle": "custom"}}, {"path": "interview/index", "type": "page", "layout": "default", "style": {"navigationBarTitleText": "", "enablePullDownRefresh": false, "navigationStyle": "custom"}}, {"path": "interview/WaitMeeting", "type": "page", "layout": "default", "style": {"navigationBarTitleText": "", "enablePullDownRefresh": false, "navigationStyle": "custom"}}, {"path": "jobDetail/hrJobDetail", "type": "page", "layout": "default", "style": {"navigationBarTitleText": "", "enablePullDownRefresh": false, "navigationStyle": "custom"}}, {"path": "jobDetail/index", "type": "page", "layout": "default", "style": {"navigationBarTitleText": "", "enablePullDownRefresh": false, "navigationStyle": "custom"}}, {"path": "jobExpectations/index", "type": "page", "layout": "default", "style": {"navigationBarTitleText": "", "enablePullDownRefresh": false, "navigationStyle": "custom"}}, {"path": "moreJobs/index", "type": "page", "layout": "default", "style": {"navigationBarTitleText": "", "enablePullDownRefresh": false, "navigationStyle": "custom"}}, {"path": "mySkill/index", "type": "page", "layout": "default", "style": {"navigationBarTitleText": "", "enablePullDownRefresh": false, "navigationStyle": "custom"}}, {"path": "onlineResume/index", "type": "page", "layout": "default", "style": {"navigationBarTitleText": "", "enablePullDownRefresh": false, "navigationStyle": "custom"}}, {"path": "pdf-upload/pdf-upload", "type": "page"}, {"path": "preview/index", "type": "page", "layout": "default", "style": {"navigationBarTitleText": "", "enablePullDownRefresh": false, "navigationStyle": "custom"}}, {"path": "projectExperience/index", "type": "page", "layout": "default", "style": {"navigationBarTitleText": "", "enablePullDownRefresh": false, "navigationStyle": "custom"}}, {"path": "projectExperience/workContent", "type": "page", "layout": "default", "style": {"navigationBarTitleText": "", "enablePullDownRefresh": false, "navigationStyle": "custom", "app-plus": {"softinputMode": "adjustResize"}}}, {"path": "projectExperience/workPerformance", "type": "page", "layout": "default", "style": {"navigationBarTitleText": "", "enablePullDownRefresh": false, "navigationStyle": "custom", "app-plus": {"softinputMode": "adjustResize"}}}, {"path": "recruiter/index", "type": "page", "layout": "default", "style": {"navigationBarTitleText": "", "enablePullDownRefresh": false, "navigationStyle": "custom"}}, {"path": "salaryWork/index", "type": "page", "layout": "default", "style": {"navigationBarTitleText": "", "navigationStyle": "custom"}}, {"path": "seekEmployment/index", "type": "page", "layout": "default", "style": {"navigationBarTitleText": "", "enablePullDownRefresh": false, "navigationStyle": "custom"}}, {"path": "violationDis/index", "type": "page", "layout": "default", "style": {"navigationBarTitleText": "", "enablePullDownRefresh": false, "navigationStyle": "custom"}}, {"path": "workExperience/dept", "type": "page", "layout": "default", "style": {"navigationBarTitleText": "", "enablePullDownRefresh": false, "navigationStyle": "custom"}}, {"path": "workExperience/index", "type": "page", "layout": "default", "style": {"navigationBarTitleText": "", "enablePullDownRefresh": false, "navigationStyle": "custom"}}, {"path": "workExperience/workContent", "type": "page", "layout": "default", "style": {"navigationBarTitleText": "", "enablePullDownRefresh": false, "navigationStyle": "custom", "app-plus": {"softinputMode": "adjustResize"}}}, {"path": "workExperience/workPerformance", "type": "page", "layout": "default", "style": {"navigationBarTitleText": "", "enablePullDownRefresh": false, "navigationStyle": "custom", "disableScroll": true, "app-plus": {"softinputMode": "adjustResize"}}}, {"path": "workExperience/workSkills", "type": "page", "layout": "default", "style": {"navigationBarTitleText": "", "enablePullDownRefresh": false, "navigationStyle": "custom"}}, {"path": "onlineResume/certificate/index", "type": "page", "layout": "default", "style": {"navigationBarTitleText": "", "enablePullDownRefresh": false, "navigationStyle": "custom"}}, {"path": "onlineResume/myAdvantage/index", "type": "page", "layout": "default", "style": {"navigationBarTitleText": "", "enablePullDownRefresh": false, "navigationStyle": "custom", "app-plus": {"softinputMode": "adjustResize"}}}, {"path": "onlineResume/portfolio/index", "type": "page", "layout": "default", "style": {"navigationBarTitleText": "", "enablePullDownRefresh": false, "navigationStyle": "custom"}}, {"path": "onlineResume/qualiCertificate/index", "type": "page", "layout": "default", "style": {"navigationBarTitleText": "", "enablePullDownRefresh": false, "navigationStyle": "custom"}}]}, {"root": "setting", "pages": [{"path": "aboutUs/index", "type": "page", "layout": "default", "style": {"navigationBarTitleText": "", "enablePullDownRefresh": false, "navigationStyle": "custom"}}, {"path": "accountMange/index", "type": "page", "layout": "default", "style": {"navigationBarTitleText": "", "enablePullDownRefresh": false, "navigationStyle": "custom"}}, {"path": "accountNumber/index", "type": "page", "layout": "default", "style": {"navigationBarTitleText": "", "enablePullDownRefresh": false, "navigationStyle": "custom"}}, {"path": "AdressMange/index", "type": "page", "layout": "default", "style": {"navigationBarTitleText": "", "enablePullDownRefresh": false, "navigationStyle": "custom"}}, {"path": "certificateImage/businesslicense", "type": "page", "layout": "default", "style": {"navigationBarTitleText": "", "enablePullDownRefresh": false, "navigationStyle": "custom"}}, {"path": "certificateImage/humanResources", "type": "page", "layout": "default", "style": {"navigationBarTitleText": "", "enablePullDownRefresh": false, "navigationStyle": "custom"}}, {"path": "certificateImage/TelServices", "type": "page", "layout": "default", "style": {"navigationBarTitleText": "", "enablePullDownRefresh": false, "navigationStyle": "custom"}}, {"path": "generalSettings/index", "type": "page", "layout": "default", "style": {"navigationBarTitleText": "", "enablePullDownRefresh": false, "navigationStyle": "custom"}}, {"path": "generalSetup/index", "type": "page", "layout": "default", "style": {"navigationBarTitleText": "", "enablePullDownRefresh": false, "navigationStyle": "custom"}}, {"path": "identityAuth/index", "type": "page", "layout": "default", "style": {"navigationBarTitleText": "", "enablePullDownRefresh": false, "navigationStyle": "custom"}}, {"path": "IdentitySwitching/index", "type": "page", "layout": "default", "style": {"navigationBarTitleText": "", "enablePullDownRefresh": false, "navigationStyle": "custom"}}, {"path": "InfoCollection/index", "type": "page", "layout": "default", "style": {"navigationBarTitleText": "", "enablePullDownRefresh": false, "navigationStyle": "custom"}}, {"path": "loginDevice/index", "type": "page", "layout": "default", "style": {"navigationBarTitleText": "", "enablePullDownRefresh": false, "navigationStyle": "custom"}}, {"path": "notice/index", "type": "page", "layout": "default", "style": {"navigationBarTitleText": "", "enablePullDownRefresh": false, "navigationStyle": "custom"}}, {"path": "permission/index", "type": "page", "layout": "default", "style": {"navigationBarTitleText": "", "enablePullDownRefresh": false, "navigationStyle": "custom"}}, {"path": "permissionList/index", "type": "page", "layout": "default", "style": {"navigationBarTitleText": "", "enablePullDownRefresh": false, "navigationStyle": "custom"}}, {"path": "personalInfo/index", "type": "page", "layout": "default", "style": {"navigationBarTitleText": "", "enablePullDownRefresh": false, "navigationStyle": "custom"}}, {"path": "PersonalInfoList/basicInfo", "type": "page", "layout": "default", "style": {"navigationBarTitleText": "", "enablePullDownRefresh": false, "navigationStyle": "custom"}}, {"path": "PersonalInfoList/IdentityInfo", "type": "page", "layout": "default", "style": {"navigationBarTitleText": "", "enablePullDownRefresh": false, "navigationStyle": "custom"}}, {"path": "PersonalInfoList/index", "type": "page", "layout": "default", "style": {"navigationBarTitleText": "", "enablePullDownRefresh": false, "navigationStyle": "custom"}}, {"path": "phoneUpdata/index", "type": "page", "layout": "default", "style": {"navigationBarTitleText": "", "enablePullDownRefresh": false, "navigationStyle": "custom"}}, {"path": "PrivacyAgreement/CancelAgreement", "type": "page", "layout": "default", "style": {"navigationBarTitleText": "", "enablePullDownRefresh": false, "navigationStyle": "custom"}}, {"path": "PrivacyAgreement/CommAgreement", "type": "page", "layout": "default", "style": {"navigationBarTitleText": "", "enablePullDownRefresh": false, "navigationStyle": "custom"}}, {"path": "PrivacyAgreement/EnterpriseResourses", "type": "page", "layout": "default", "style": {"navigationBarTitleText": "", "enablePullDownRefresh": false, "navigationStyle": "custom"}}, {"path": "PrivacyAgreement/HumanResources", "type": "page", "layout": "default", "style": {"navigationBarTitleText": "", "enablePullDownRefresh": false, "navigationStyle": "custom"}}, {"path": "PrivacyAgreement/index", "type": "page", "layout": "default", "style": {"navigationBarTitleText": "", "enablePullDownRefresh": false, "navigationStyle": "custom"}}, {"path": "PrivacyAgreement/privacy", "type": "page", "layout": "default", "style": {"navigationBarTitleText": "", "enablePullDownRefresh": false, "navigationStyle": "custom"}}, {"path": "PrivacyAgreement/PrivacyPolicy", "type": "page", "layout": "default", "style": {"navigationBarTitleText": "", "enablePullDownRefresh": false, "navigationStyle": "custom"}}, {"path": "PrivacyAgreement/Recruitmentbehavior", "type": "page", "layout": "default", "style": {"navigationBarTitleText": "", "enablePullDownRefresh": false, "navigationStyle": "custom"}}, {"path": "PrivacyAgreement/ResumeUser", "type": "page", "layout": "default", "style": {"navigationBarTitleText": "", "enablePullDownRefresh": false, "navigationStyle": "custom"}}, {"path": "PrivacyAgreement/UserAgreement", "type": "page", "layout": "default", "style": {"navigationBarTitleText": "", "enablePullDownRefresh": false, "navigationStyle": "custom"}}, {"path": "PrivacyAgreement/UserRules", "type": "page", "layout": "default", "style": {"navigationBarTitleText": "", "enablePullDownRefresh": false, "navigationStyle": "custom"}}, {"path": "setting/index", "type": "page", "layout": "default", "style": {"navigationBarTitleText": "", "enablePullDownRefresh": false, "navigationStyle": "custom"}}, {"path": "sharingInfoList/index", "type": "page", "layout": "default", "style": {"navigationBarTitleText": "", "enablePullDownRefresh": false, "navigationStyle": "custom"}}, {"path": "tourist/index", "type": "page", "layout": "default", "style": {"navigationBarTitleText": "", "navigationStyle": "custom", "app-plus": {"popGesture": "none"}}}, {"path": "wxUpdata/index", "type": "page", "layout": "default", "style": {"navigationBarTitleText": "", "enablePullDownRefresh": false, "navigationStyle": "custom"}}]}, {"root": "sub_business", "pages": [{"path": "pages/AddressCenter/AddressAdd", "type": "page", "layout": "default", "style": {"navigationBarTitleText": "", "enablePullDownRefresh": false, "navigationStyle": "custom"}}, {"path": "pages/AddressCenter/index", "type": "page", "layout": "default", "style": {"navigationBarTitleText": "", "enablePullDownRefresh": false, "navigationStyle": "custom"}}, {"path": "pages/communicate/index", "type": "page", "layout": "default", "style": {"navigationBarTitleText": "", "enablePullDownRefresh": false, "navigationStyle": "custom"}}, {"path": "pages/company/index", "type": "page", "layout": "default", "style": {"navigationBarTitleText": "", "enablePullDownRefresh": false, "navigationStyle": "custom"}}, {"path": "pages/conversation/remark", "type": "page", "layout": "default", "style": {"navigationBarTitleText": "备注", "navigationBarBackgroundColor": "#ffffff"}}, {"path": "pages/conversation/report", "type": "page", "layout": "default", "style": {"navigationBarTitleText": "举报", "navigationBarBackgroundColor": "#F0F0F0"}}, {"path": "pages/conversation/setting", "type": "page", "layout": "default", "style": {"navigationBarTitleText": "", "navigationStyle": "custom"}}, {"path": "pages/inappropriate/index", "type": "page", "layout": "default", "style": {"navigationBarTitleText": "", "enablePullDownRefresh": false, "navigationStyle": "custom"}}, {"path": "pages/interview/detail", "type": "page", "layout": "default", "style": {"navigationBarTitleText": "", "enablePullDownRefresh": false, "navigationStyle": "custom"}}, {"path": "pages/interview/index", "type": "page", "layout": "default", "style": {"navigationBarTitleText": "", "enablePullDownRefresh": false, "navigationStyle": "custom"}}, {"path": "pages/interview/Initiate", "type": "page", "layout": "default", "style": {"navigationBarTitleText": "", "enablePullDownRefresh": false, "navigationStyle": "custom"}}, {"path": "pages/myInfo/index", "type": "page", "layout": "default", "style": {"navigationBarTitleText": "", "enablePullDownRefresh": false, "navigationStyle": "custom"}}, {"path": "pages/order/detail", "type": "page", "layout": "default", "style": {"navigationBarTitleText": "详情", "navigationStyle": "custom"}}, {"path": "pages/order/index", "type": "page", "layout": "default", "style": {"navigationBarTitleText": "使用记录", "navigationStyle": "custom"}}, {"path": "pages/positionManage/index", "type": "page", "layout": "default", "style": {"navigationBarTitleText": "岗位管理", "enablePullDownRefresh": false, "navigationStyle": "custom"}}, {"path": "pages/prop/cdk", "type": "page", "layout": "default", "style": {"navigationBarTitleText": "CDK兑换", "navigationStyle": "custom"}}, {"path": "pages/prop/details", "type": "page", "layout": "default", "style": {"navigationBarTitleText": "道具详情", "navigationStyle": "custom"}}, {"path": "pages/prop/index", "type": "page", "layout": "default", "style": {"navigationBarTitleText": "道具", "navigationStyle": "custom"}}, {"path": "pages/prop/pay", "type": "page", "layout": "default", "style": {"navigationBarTitleText": "", "navigationStyle": "custom"}}, {"path": "pages/prop/record", "type": "page", "layout": "default", "style": {"navigationBarTitleText": "使用记录", "navigationStyle": "custom"}}, {"path": "pages/prop/redetail", "type": "page", "layout": "default", "style": {"navigationBarTitleText": "使用记录", "navigationStyle": "custom"}}, {"path": "pages/prop/remorelist", "type": "page", "layout": "default", "style": {"navigationBarTitleText": "更多记录", "navigationStyle": "custom"}}, {"path": "pages/rapid-processing/index", "type": "page", "layout": "default", "style": {"navigationBarTitleText": "快速处理", "navigationStyle": "custom"}}, {"path": "pages/recruitmentData/index", "type": "page", "layout": "default", "style": {"navigationBarTitleText": "招聘数据", "navigationStyle": "custom"}}, {"path": "pages/release/index", "type": "page", "layout": "default", "style": {"navigationBarTitleText": "", "navigationStyle": "custom"}}, {"path": "pages/service/index", "type": "page", "layout": "default", "style": {"navigationBarTitleText": "", "enablePullDownRefresh": false, "navigationStyle": "custom"}}, {"path": "pages/setting/index", "type": "page", "layout": "default", "style": {"navigationBarTitleText": "", "enablePullDownRefresh": false, "navigationStyle": "custom"}}, {"path": "pages/tucked/index", "type": "page", "layout": "default", "style": {"navigationBarTitleText": "", "enablePullDownRefresh": false, "navigationStyle": "custom"}}, {"path": "pages/walletInvoice/index", "type": "page", "layout": "default", "style": {"navigationBarTitleText": "钱包", "navigationStyle": "custom"}}, {"path": "pages/company/model/companyBenefit", "type": "page", "layout": "default", "style": {"navigationBarTitleText": "", "navigationStyle": "custom"}}, {"path": "pages/company/model/companyProfile", "type": "page", "layout": "default", "style": {"navigationBarTitleText": "", "enablePullDownRefresh": false, "navigationStyle": "custom", "app-plus": {"softinputMode": "adjustResize"}}}, {"path": "pages/company/model/companyStyleList", "type": "page", "layout": "default", "style": {"navigationBarTitleText": "", "enablePullDownRefresh": false, "navigationStyle": "custom"}}, {"path": "pages/company/model/shortCompany", "type": "page", "layout": "default", "style": {"navigationBarTitleText": "", "enablePullDownRefresh": false, "navigationStyle": "custom"}}, {"path": "pages/myInfo/model/hrPosition", "type": "page", "layout": "default", "style": {"navigationBarTitleText": "", "enablePullDownRefresh": false, "navigationStyle": "custom"}}, {"path": "pages/prop/coupons/index", "type": "page", "layout": "default", "style": {"navigationBarTitleText": "优惠卷", "enablePullDownRefresh": false, "navigationStyle": "custom"}}, {"path": "pages/setting/model/MessageSettings", "type": "page", "layout": "default", "style": {"navigationBarTitleText": "", "enablePullDownRefresh": false, "navigationStyle": "custom"}}, {"path": "pages/setting/model/PrivacyAgreement", "type": "page", "layout": "default", "style": {"navigationBarTitleText": "", "enablePullDownRefresh": false, "navigationStyle": "custom"}}, {"path": "pages/walletInvoice/InvoiceApplication/index", "type": "page", "layout": "default", "style": {"navigationBarTitleText": "", "navigationStyle": "custom"}}, {"path": "pages/walletInvoice/InvoiceRecord/index", "type": "page", "layout": "default", "style": {"navigationBarTitleText": "发票记录", "navigationStyle": "custom"}}, {"path": "pages/walletInvoice/Invoicing/index", "type": "page", "layout": "default", "style": {"navigationBarTitleText": "开发票", "navigationStyle": "custom"}}, {"path": "pages/walletInvoice/rechargeDetails/index", "type": "page", "layout": "default", "style": {"navigationBarTitleText": "充值明细", "navigationStyle": "custom"}}, {"path": "pages/setting/model/aboutUs/index", "type": "page", "layout": "default", "style": {"navigationBarTitleText": "", "enablePullDownRefresh": false, "navigationStyle": "custom"}}, {"path": "pages/setting/model/accountInfo/index", "type": "page", "layout": "default", "style": {"navigationBarTitleText": "", "enablePullDownRefresh": false, "navigationStyle": "custom"}}, {"path": "pages/setting/model/accountSecurity/index", "type": "page", "layout": "default", "style": {"navigationBarTitleText": "", "enablePullDownRefresh": false, "navigationStyle": "custom"}}, {"path": "pages/setting/model/accountSecurity/PasswordChange", "type": "page", "layout": "default", "style": {"navigationBarTitleText": "", "enablePullDownRefresh": false, "navigationStyle": "custom"}}, {"path": "pages/setting/model/accountSecurity/PhoneChange", "type": "page", "layout": "default", "style": {"navigationBarTitleText": "", "enablePullDownRefresh": false, "navigationStyle": "custom"}}, {"path": "pages/setting/model/blacklist/index", "type": "page", "layout": "default", "style": {"navigationBarTitleText": "", "enablePullDownRefresh": false, "navigationStyle": "custom"}}, {"path": "pages/setting/model/clearCache/index", "type": "page", "layout": "default", "style": {"navigationBarTitleText": "", "enablePullDownRefresh": false, "navigationStyle": "custom"}}, {"path": "pages/setting/model/deregisterAccount/index", "type": "page", "layout": "default", "style": {"navigationBarTitleText": "", "enablePullDownRefresh": false, "navigationStyle": "custom"}}, {"path": "pages/setting/model/InfoCollection/index", "type": "page", "layout": "default", "style": {"navigationBarTitleText": "", "enablePullDownRefresh": false, "navigationStyle": "custom"}}, {"path": "pages/setting/model/loginDevice/index", "type": "page", "layout": "default", "style": {"navigationBarTitleText": "", "enablePullDownRefresh": false, "navigationStyle": "custom"}}, {"path": "pages/setting/model/message/CommonGreetings", "type": "page", "layout": "default", "style": {"navigationBarTitleText": "", "enablePullDownRefresh": false, "navigationStyle": "custom", "app-plus": {"softinputMode": "adjustResize"}}}, {"path": "pages/setting/model/message/CommonPhrases", "type": "page", "layout": "default", "style": {"navigationBarTitleText": "", "enablePullDownRefresh": false, "navigationStyle": "custom"}}, {"path": "pages/setting/model/message/MessageSettings", "type": "page", "layout": "default", "style": {"navigationBarTitleText": "", "enablePullDownRefresh": false, "navigationStyle": "custom"}}, {"path": "pages/setting/model/message/recommend", "type": "page", "layout": "default", "style": {"navigationBarTitleText": "", "enablePullDownRefresh": false, "navigationStyle": "custom"}}, {"path": "pages/setting/model/permissionManage/index", "type": "page", "layout": "default", "style": {"navigationBarTitleText": "", "enablePullDownRefresh": false, "navigationStyle": "custom"}}, {"path": "pages/setting/model/PersonalInfoList/basicInfo", "type": "page", "layout": "default", "style": {"navigationBarTitleText": "", "enablePullDownRefresh": false, "navigationStyle": "custom"}}, {"path": "pages/setting/model/PersonalInfoList/IdentityInfo", "type": "page", "layout": "default", "style": {"navigationBarTitleText": "", "enablePullDownRefresh": false, "navigationStyle": "custom"}}, {"path": "pages/setting/model/PersonalInfoList/index", "type": "page", "layout": "default", "style": {"navigationBarTitleText": "", "enablePullDownRefresh": false, "navigationStyle": "custom"}}, {"path": "pages/setting/model/qualifications/index", "type": "page", "layout": "default", "style": {"navigationBarTitleText": "", "enablePullDownRefresh": false, "navigationStyle": "custom"}}, {"path": "pages/setting/model/Resignation/index", "type": "page", "layout": "default", "style": {"navigationBarTitleText": "", "enablePullDownRefresh": false, "navigationStyle": "custom"}}]}, {"root": "sub_common", "pages": [{"path": "pages/phrases/add", "type": "page", "layout": "default", "style": {"navigationBarTitleText": "添加常用语", "navigationStyle": "custom"}}, {"path": "pages/phrases/index", "type": "page", "layout": "default", "style": {"navigationBarTitleText": "常用语", "navigationStyle": "custom"}}]}, {"root": "ChatUIKit", "pages": [{"path": "modules/Chat/index", "style": {"navigationStyle": "custom", "disableScroll": true, "app-plus": {"bounce": "none", "softinputNavBar": "none"}}}, {"path": "modules/VideoPreview/index", "style": {"navigationBarTitleText": "Video Preview", "app-plus": {"bounce": "none"}}}]}], "pages": [{"path": "pages/login/index", "type": "home", "layout": "default", "style": {"navigationBarTitleText": "", "enablePullDownRefresh": false, "navigationStyle": "custom"}}, {"path": "pages/deepseek/index", "type": "page", "layout": "default", "style": {"navigationBarTitleText": "", "enablePullDownRefresh": false, "navigationStyle": "custom"}}, {"path": "pages/guide/index", "type": "page", "layout": "default", "style": {"navigationBarTitleText": "", "enablePullDownRefresh": false, "navigationStyle": "custom"}}, {"path": "pages/home/<USER>", "type": "page", "layout": "default", "style": {"navigationBarTitleText": "", "enablePullDownRefresh": false, "navigationStyle": "custom"}}, {"path": "pages/mine/index", "type": "page", "layout": "default", "style": {"navigationBarTitleText": "", "enablePullDownRefresh": false, "navigationStyle": "custom"}}, {"path": "pages/news/index", "type": "page", "layout": "default", "style": {"navigationBarTitleText": "", "enablePullDownRefresh": false, "navigationStyle": "custom"}}]}