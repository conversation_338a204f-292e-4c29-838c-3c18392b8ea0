<route lang="json5">
{
  layout: 'default',
  style: {
    navigationBarTitleText: '',
    enablePullDownRefresh: false,
    navigationStyle: 'custom',
  },
}
</route>
<template>
  <z-paging ref="pagingRef" layout-only :paging-style="pageStyle">
    <template #top>
      <CustomNavBar></CustomNavBar>
    </template>
    <view class="page-container">
      <!-- 协议头部 -->
      <view class="protocol-header">
        <text class="protocol-title">易直聘直聘沟通行为规范</text>
        <view class="protocol-meta">
          <text class="version">版本：ver202505</text>
          <text class="date">生效日期：2025年05年24日</text>
        </view>
      </view>

      <!-- 目录导航 -->
      <view class="directory">
        <view class="directory-list">
          <view
            class="directory-item"
            v-for="(item, index) in directory"
            :key="index"
            @click="scrollToSection(index)"
          >
            <!-- <text class="directory-icon">•</text> -->
            <text class="directory-text">{{ item }}</text>
          </view>
        </view>
      </view>

      <!-- 协议内容 -->
      <!-- 第一条 -->
      <view class="protocol-section" id="section-0">
        <text class="section-title">一、禁止侮辱谩骂</text>
        <view class="section-content">
          <text class="section-text">
            严禁使用人身攻击（如“蠢货”“废物”）、地域或学历歧视（如“乡下人”“专科没用”）、性羞辱（如“陪睡可入职”）、外貌侮辱（如“丑八怪”）等言论。禁止通过缩写、谐音、表情包、图片等变体形式实施辱骂（如“SB”“**”符号替代”等）。平台将结合举报内容、历史沟通记录及相关证据，进行主观合理判断，并有权根据具体情形决定是否处理及采取包括但不限于警告、禁言、限制功能、封禁账号等措施。对于争议较大或有歧义的表达，平台有权作出最终解释和处理决定。
          </text>
        </view>
      </view>
      <!-- 第二条 -->
      <view class="protocol-section" id="section-1">
        <text class="section-title">二、禁止骚扰威胁</text>
        <view class="section-content">
          <text class="section-text">
            1.禁止威胁他人人身安全（如“我知道您住哪里”）、职业发展（如“不听话就行业封杀”）、薪资胁迫（如“发裸照才给工资”）或利用其他经济利益实施胁迫。
          </text>
          <text class="section-text">
            2.禁止高频发送无意义信息（如连续10条以上重复消息）、恶意刷屏或要求视频裸聊等行为。平台有合理理由怀疑用户存在骚扰、威胁行为时，可先行采取暂时性限制措施，并待进一步核实时调整处理结果。
          </text>
        </view>
      </view>
      <!-- 第三条 -->
      <view class="protocol-section" id="section-2">
        <text class="section-title">三、禁止违法违规信息</text>
        <view class="section-content">
          <text class="section-text">
            1.严禁发布或变相协助传播下列违法违规内容，包括但不限于：传销、集资诈骗（如“投资返利”）、伪造证件、代孕、贩卖隐私数据、违禁药品（如迷药）、非法劳务（如“黑工渠道”）、赌博、色情直播、刷单兼职、诱导他人参与违法犯罪、散布虚假信息、谣言、侵犯他人知识产权、名誉权、隐私权、非法买卖或泄露个人信息、非法集会、非法募捐、恐怖主义、极端主义、危害国家安全、扰乱社会秩序、以及其他违反国家法律法规及平台公示规则的行为。
          </text>
          <text class="section-text">
            2.用户发布本条所述信息的，视为自动授权平台为调查、处理相关违规行为之目的，追溯、收集、分析其使用平台时的相关设备信息，包括但不限于设备型号、操作系统、唯一设备识别码、IP地址、登录地理位置、操作日志、账号信息、通讯录、网络环境等。平台有权根据实际情况将相关信息提供给有权机关或依法配合调查。平台承诺对收集的信息依法严格保密，除依法依约或经用户同意外，不得擅自对外披露。
          </text>
        </view>
      </view>
      <!-- 第四条 -->
      <view class="protocol-section" id="section-3">
        <text class="section-title">四、禁止泄露隐私</text>
        <view class="section-content">
          <text class="section-text">
            不得以任何形式强制或公开索要、收集、存储、使用、传输、披露包括但不限于下列个人信息及敏感数据：身份证正反面、户籍地址、病史、联系方式、银行账户、薪资流水、学历证书或证明、社交账号及密码、人脸照片、人脸识别信息、工作证件、社会保险信息、税单记录、住宿地址、亲属关系、婚姻状况等，以及因技术升级和法律规定应纳入保护范围的其他个人信息。
          </text>
        </view>
      </view>
      <!-- 第五条 -->
      <view class="protocol-section" id="section-4">
        <text class="section-title">五、禁止广告引流</text>
        <view class="section-content">
          <text class="section-text">
            1.禁止发布微商、网贷、医美整形、游戏代练、虚拟货币交易等非招聘类广告，包括但不限于上述广告行为。
          </text>
          <text class="section-text">
            2.广告引流行为包括但不限于通过发布文字、图片、语音、视频、链接、二维码、联系方式、账号、群组邀请、推广码、优惠券、第三方小程序等方式，诱导或引导用户跳转至其他平台、加入第三方群组、关注非招聘相关账号或参与非招聘相关活动。
          </text>
          <text class="section-text">
            3.平台有权结合技术监测、用户举报及人工审核，自主认定广告性质及违规行为，并有权根据违规行为的性质和危害程度采取包括但不限于警告、禁言、封号、限制功能、公开通报等处理措施。对于争议较大的情形，平台有权作出最终解释。
          </text>
        </view>
      </view>
      <!-- 第六条 -->
      <view class="protocol-section" id="section-5">
        <text class="section-title">六、真实身份认证</text>
        <view class="section-content">
          <text class="section-text">
            1.企业账号需上传有效期内的营业执照、在职证明及HR身份信息，严禁使用已注销、吊销或虚假公司信息。企业应确保所提交认证材料的真实性、合法性和有效性。认证信息失效前30日，平台将通过站内信或注册邮箱发送提示通知，视为已尽到告知义务。企业账号认证信息发生变更时，须在30日内通过平台完成更新及备案。逾期未更新、提交虚假、过期或伪造信息的，平台有权暂停或终止相关账号服务，并视情节严重程度采取包括但不限于警告、冻结、封禁等处理措施。因企业未按要求履行认证义务导致的全部责任及后果，由企业自行承担，平台保留追究相关法律责任的权利。
          </text>
          <text class="section-text">
            2.个人用户头像须为本人近期真实照片，禁止使用明星、网络图片、动物、卡通形象等非本人照片。昵称不得含有“HR”“招聘”等具有误导性的词汇或符号。用户因上传非本人照片或使用他人肖像、受版权保护的图片等导致的侵权责任，由其自行承担。平台有权对涉嫌违规的头像或昵称进行审核、下架、警告、限制使用或封禁处理。
          </text>
        </view>
      </view>
      <!-- 第七条 -->
      <view class="protocol-section" id="section-6">
        <text class="section-title">七、违规处理机制</text>
        <view class="section-content">
          <text class="section-text">
            1.文字/语音辱骂：首次屏蔽消息并警告，二次违规禁言3天，三次及以上采取阶梯式处罚措施，包括但不限于延长禁言、限制功能、封禁账号等。
          </text>
          <text class="section-text">
            2. 发布违法信息：直接永久封号，并保留向相关主管部门（如网信、公安等）报告的权利。
          </text>
          <text class="section-text">
            3. 虚假招聘：冻结账号，需重新提交企业资质并通过平台人工审核后方可恢复使用。
          </text>
          <text class="section-text">
            4.
            用户可通过聊天框“举报”按钮提交证据，平台将在48小时内完成初步核查反馈处理结果；如因案情复杂需要延长处理期限的，平台可另行通知用户。
          </text>
          <text class="section-text">
            5. 同一违规行为被不同用户重复举报的，按单次违规计算处理次数。
          </text>
          <text class="section-text">
            6.
            平台可根据违规行为的性质、危害程度及社会影响，不受处理次数限制，直接启动顶格处罚措施，包括但不限于永久封号、公开通报、移交有关部门等。
          </text>
          <text class="section-text">
            7. 平台有权根据实际情况对违规处理流程进行调整，并对争议较大的情形作出最终解释。
          </text>
        </view>
      </view>
      <!-- 第八条 -->
      <view class="protocol-section" id="section-7">
        <text class="section-title">八、未成年人保护</text>
        <view class="section-content">
          <text class="section-text">
            企业发布岗位时，须在岗位信息中明确标注“仅招16周岁以上”，并确保招聘流程中不针对未成年人进行岗位推送或录用。企业及其工作人员在与用户沟通时，不得以任何形式询问未成年人的学校地址、父母联系方式等个人敏感信息，亦不得诱导、教唆未成年人逃课、离家或从事其他危害其身心健康的行为。平台如发现涉及未成年人高危行为，有权先行采取包括但不限于冻结相关账户、限制功能、暂停服务等紧急措施，并在处理后及时通知企业。平台应定期自查和完善未成年人保护机制，发现未成年人异常注册、登录或使用行为时，有权临时冻结账号并启动身份复核程序。平台已尽到合理审核和管理义务的，因用户提供虚假信息或规避平台审核导致的后果，由相关用户自行承担，平台不承担任何责任。
          </text>
        </view>
      </view>
      <!-- 第九条 -->
      <view class="protocol-section" id="section-8">
        <text class="section-title">九、招聘信息规范</text>
        <view class="section-content">
          <text class="section-text">
            1.岗位描述须包括明确的薪资范围（如“5000-8000元”）、具体工作地点（建议详细至楼层或具体区域，如因实际原因不便披露的，应在岗位信息中说明原因）、合同类型（全职/兼职/实习），严禁使用“薪资面议”“待遇优厚”等模糊表述。企业应对其所发布岗位信息的真实性、准确性、合法性和完整性承担全部责任。平台仅对岗位信息进行合理范围内的形式审查，不对岗位信息的真实性、合法性和完整性承担实质性审核或担保责任。因企业信息不实或违规发布造成的全部责任及后果，由企业自行承担，平台有权根据实际情况采取包括但不限于下架、警告、冻结、封禁等处理措施。。
          </text>
          <text class="section-text">
            2.不得虚构福利（如“免费出国旅游”未兑现）、隐藏强制条款（如“入职交押金”）。平台建立虚假福利举报、核查及公告流程，对查证属实的虚假信息，有权公开处理结果以提升透明度，并有权对相关企业采取包括但不限于下架、警告、冻结、封禁等处理措施。因企业发布虚假福利或隐藏强制条款造成的全部责任及后果，由企业自行承担，平台保留追究相关法律责任的权利。
            。
          </text>
        </view>
      </view>
      <!-- 第十条 -->
      <view class="protocol-section" id="section-9">
        <text class="section-title">十、文明沟通要求</text>
        <view class="section-content">
          <text class="section-text">
            1.平台倡导企业在24小时内回复求职者咨询，止使禁用“急招！！！”等夸张表述。平对此类沟通不承担强制监管或保证所有咨询均获及时答复的义务，如因信息延迟或表述夸张等引发的纠纷或损失，平台不承担任何责任。
          </text>
          <text class="section-text">
            2.建议企业使用标准化话术（如“请发送简历至邮箱”），避免长段语音（单条不超过30秒）或全英文/方言沟通。单日沟通频次超过行业均值200%的账号，将自动触发平台风控监测，平台有权进行进一步核查，必要时可采取警告、限制功能、临时冻结等措施
          </text>
          <text class="section-text">
            3.禁止通过重复发送问号、感叹号等符号刷屏（如“？？？”、“快点！！！”）或其他具有压迫性、骚扰性质的表达。平台可将具体沟通场景、风控监测及用户举报情况，独立判断是否构成违规，并有权视情节严重程度采取包括但不限于警告、禁言、限制功能等处理措施。
          </text>
        </view>
      </view>
      <!-- 第十一条 -->
      <view class="protocol-section" id="section-10">
        <text class="section-title">十一、争议解决指引</text>
        <view class="section-content">
          <text class="section-text">
            1.薪资/合同纠纷应优先通过平台“在线仲裁”通道解决，用户应积极配合平台处理流程。平台在线仲裁通道处理结果，不影响用户依法向有管辖权的仲裁机构、人民法院提起诉讼的权利。严禁任何一方在平台内以威胁恐吓、侮辱等方式扰乱平台秩序或影响他人合法权益。平台有权对扰乱秩序的行为采取包括但不限于警告、禁言、限制功能、封禁账号等处理措施。
          </text>
          <text class="section-text">
            2.用户就赔偿金额等事项协商达成协议的，须通过平台提供的电子合同系统进行操作并完成备案，不得仅以平台聊天功能或其他非正式沟通渠道成成或存储赔偿协议。任何未通过平台电子合同系统备案的赔偿协议，平台有权不予认可或支持。
            经在线仲裁通道提交的材料，均视为双方认可的有效证据。平台有权根据争议情况及实际需要，对争议的处理方式作出最终解释和适当调整。
          </text>
        </view>
      </view>
      <!-- 第十二条 -->
      <view class="protocol-section" id="section-11">
        <text class="section-title">十二、数据安全条款</text>
        <view class="section-content">
          <text class="section-text">
            聊天记录加密存储，离职招聘者账号由企业管理员应在72小时内完成注销处理，注销后应确保相关账户及数据依法及时销毁、不可恢复。招聘者、企业及其他用户均不得下载、存储或以任何形式对外传递平台聊天数据，除经平台书面许可或授权的第三方服务机构，以及司法机关依法要求的情形外。违反上述规定的，平台有权采取包括但不限于冻结账号、终止服务、追究责任等处理措施。
          </text>
        </view>
      </view>
      <!-- 第十三条 -->
      <view class="protocol-section" id="section-12">
        <text class="section-title">十三、其他条款</text>
        <view class="section-content">
          <text class="section-text">
            1.通知与送达条款：平台向用户发出的各类通知、告知、警示、处理决定等，可通过平台站内信、用户注册邮箱、页面弹窗或用户预留的其他有效联系方式送达，自发送成功之时起满24小时，即视为用户已收到相关通知。用户应确保预留信息准确、畅通，因未及时更新或查收导致的风险及后果，由用户自行承担。
          </text>
          <text class="section-text">
            2.生效条款：用户勾选同意本规范即视为本规范生效，与纸质合同具有同等法律效力。
          </text>
          <text class="section-text">
            3.终止条款：用户账号被平台永久封禁后，本规范项下的权利义务即行终止，但平台对已产生的数据依法承担留存义务。平台留存的数据范围、期限及用途应遵守国家法律法规的相关规定，并可用于配合监管、争议处理、合规审计等合法目的。
          </text>
          <text class="section-text">
            4.保密条款：用户不得向第三方披露平台安全技术措施及运营数据，该保密义务不因合同终止而失效。
          </text>
          <text class="section-text">
            5.附件条款：用户隐私政策、社区准则等作为本规范的附件，与本规范具有同等法律效力。
          </text>
        </view>
        <view class="footer">
          <text class="footer-text">生效日期：2025年05年24日</text>
          <text class="footer-text">版本：ver202505</text>
        </view>
      </view>
    </view>
  </z-paging>
</template>

<script setup lang="ts">
import CustomNavBar from '@/components/CustomNavBar/CustomNavBar.vue'

// 添加 z-paging 引用
const pagingRef = ref(null)

const { pageStyle } = usePaging({
  style: {
    background: 'linear-gradient(155deg, #FFDEDE 0%, #EBEFFA 18%, #FFFFFF 44%, #FFFFFF 100%)',
  },
})
const directory = ref([
  '一、禁止侮辱谩骂',
  '二、禁止骚扰威胁',
  '三、禁止违法违规信息',
  '四、禁止泄露隐私',
  '五、禁止广告引流',
  '六、真实身份认证',
  '七、违规处理机制',
  '八、未成年人保护',
  '九、招聘信息规范',
  '十、文明沟通要求',
  '十一、争议解决指引',
  '十二、数据安全条款',
  '十三、其他条款',
])

// 直接定位到指定章节 (适配 z-paging)
const scrollToSection = (index: number) => {
  const sectionId = `section-${index}`

  // 在 z-paging 组件中，需要使用组件内部的查询方法
  const query = uni.createSelectorQuery().in(pagingRef.value)

  // 获取目标元素的位置
  query
    .select(`#${sectionId}`)
    .boundingClientRect((targetRect) => {
      if (targetRect && !Array.isArray(targetRect)) {
        // 获取导航栏高度
        const navQuery = uni.createSelectorQuery()
        navQuery
          .select('.customNavbar')
          .boundingClientRect((navBarRect) => {
            let offsetTop = 150 // 默认偏移量

            if (navBarRect && !Array.isArray(navBarRect)) {
              offsetTop = navBarRect.height + 30 // 导航栏高度 + 额外间距
            }

            // 计算目标滚动位置
            const targetScrollTop = targetRect.top - offsetTop

            // 使用 z-paging 的滚动方法
            if (pagingRef.value) {
              try {
                // 方法1: 尝试使用 z-paging 的 scrollToY 方法
                pagingRef.value.scrollToY(Math.max(0, targetScrollTop), false)
              } catch (error) {
                // 方法2: 尝试使用 uni.pageScrollTo
                uni.pageScrollTo({
                  scrollTop: Math.max(0, targetScrollTop),
                  duration: 0,
                  success: () => {
                    console.log('uni.pageScrollTo 成功')
                  },
                })
              }
            }
          })
          .exec()
      }
    })
    .exec()
}
</script>
<style scoped lang="scss">
.setting {
  padding: 0rpx 40rpx;
  .setting-list {
    padding: 30rpx 20rpx;
  }
}
.agreement-container {
  display: flex;
  flex-direction: column;
  height: 100vh;
  padding: 20rpx 40rpx;
}

.header {
  margin-bottom: 30rpx;
  text-align: center;
}

.title {
  display: block;
  margin-bottom: 10rpx;
  font-size: 36rpx;
  font-weight: bold;
}

.version,
.effective-date {
  display: block;
  font-size: 24rpx;
  color: #666;
}

.content-list {
  padding: 20rpx 20rpx 0rpx;
  border-radius: 12rpx;
}

.list-title {
  display: block;
  margin-bottom: 20rpx;
  font-size: 28rpx;
  font-weight: bold;
}

.list-item {
  display: flex;
  margin-bottom: 15rpx;
}

.list-icon {
  margin-right: 10rpx;
}

.list-text {
  font-size: 26rpx;
  color: #333;
}

.agreement-content {
  flex: 1;
  padding: 20rpx;
  margin-bottom: 20rpx;
  border-radius: 12rpx;
}

.article {
  margin-bottom: 40rpx;
}

.article-title {
  display: block;
  margin-bottom: 20rpx;
  font-size: 30rpx;
  font-weight: bold;
}

.clause {
  margin-bottom: 15rpx;
}

.clause-text {
  font-size: 26rpx;
  line-height: 1.6;
  color: #333;
}

.footer {
  margin-top: 40rpx;
  text-align: right;
}

.footer-text {
  display: block;
  margin-bottom: 10rpx;
  font-size: 24rpx;
  color: #666666;
}

.agree-btn,
.disagree-btn {
  width: 48%;
  height: 80rpx;
  font-size: 28rpx;
  line-height: 80rpx;
  border-radius: 40rpx;
}

.agree-btn {
  color: #fff;
}

.disagree-btn {
  color: #666;
  border: 1rpx solid #ddd;
}
.page-container {
  display: flex;
  flex-direction: column;
  padding: 20rpx 40rpx;
}

.protocol-header {
  padding: 20rpx;

  text-align: center;
  border-radius: 12rpx;
}

.protocol-title {
  display: block;
  margin-bottom: 10rpx;
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
}

.protocol-meta {
  display: flex;
  gap: 30rpx;
  justify-content: center;
}

.version,
.date {
  font-size: 24rpx;
  color: #666;
}

.directory {
  padding: 20rpx;
  margin-bottom: 20rpx;
  border-radius: 12rpx;
}

.directory-title {
  display: block;
  margin-bottom: 15rpx;
  font-size: 28rpx;
  font-weight: bold;
  color: #333;
}

.directory-item {
  display: flex;
  align-items: flex-start;
  padding: 10rpx;
  margin-bottom: 10rpx;
  cursor: pointer;
  border-radius: 8rpx;

  .directory-text {
    flex: 1;
    font-size: 26rpx;
    color: #007aff;
  }
}

.directory-icon {
  margin-right: 10rpx;
}

.protocol-content {
  flex: 1;
  //   padding: 20rpx;
  margin-bottom: 20rpx;
  border-radius: 12rpx;
}

.protocol-section {
  margin-bottom: 40rpx;
}

.section-title {
  display: block;
  margin-bottom: 15rpx;
  font-size: 30rpx;
  font-weight: bold;
}

.section-content {
  padding-left: 20rpx;
}

.section-text {
  display: block;
  margin-bottom: 10rpx;
  font-size: 28rpx;
  line-height: 1.8;
  color: #333;
}

.action-buttons {
  position: sticky;
  bottom: 0;
  display: flex;
  justify-content: space-between;
  padding: 20rpx 0;
}

.agree-button,
.disagree-button {
  width: 48%;
  height: 80rpx;
  font-size: 28rpx;
  line-height: 80rpx;
  border-radius: 40rpx;
}

.agree-button {
  color: #fff;
}

.disagree-button {
  color: #666;
  border: 1rpx solid #ddd;
}
</style>
